# Binance API Configuration
# Get your API keys from https://www.binance.com/en/my/settings/api-management

# Production API Keys
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

# Testnet API Keys (for testing)
BINANCE_TESTNET_API_KEY=your_testnet_api_key_here
BINANCE_TESTNET_API_SECRET=your_testnet_api_secret_here

# Trading Configuration
USE_TESTNET=true
TRADING_SYMBOL=BTCUSDT
TIMEFRAME=15m
INITIAL_BALANCE=100
ENABLE_REAL_TRADING=false

# Risk Management
MAX_DAILY_TRADES=10
MAX_CONCURRENT_POSITIONS=1
EMERGENCY_STOP_LOSS=0.05

# Logging Configuration
LOG_LEVEL=INFO
ENABLE_FILE_LOGGING=true
ENABLE_WEB_LOGGING=true
LOG_DIRECTORY=./results/logs
MAX_LOG_FILES=30
MAX_LOG_SIZE=10MB

# Web Server Configuration
WEB_SERVER_ENABLED=true
WEB_SERVER_PORT=3000
WEB_SERVER_HOST=localhost
WEB_STATIC_PATH=./web

# Strategy Configuration
STRATEGY_NAME=Ultimate8x
TARGET_MULTIPLIER=8

# Advanced Settings
ENABLE_PAPER_TRADING=true
ENABLE_BACKTESTING=true
DATA_DIRECTORY=./data/datasets
RESULTS_DIRECTORY=./results

# Notification Settings (Optional)
ENABLE_NOTIFICATIONS=false
DISCORD_WEBHOOK_URL=
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=

# Database Configuration (Optional)
ENABLE_DATABASE=false
DATABASE_URL=
DATABASE_TYPE=sqlite

# Performance Settings
ANALYSIS_INTERVAL=10000
DATA_RETENTION_DAYS=30
MAX_MEMORY_USAGE=512MB
