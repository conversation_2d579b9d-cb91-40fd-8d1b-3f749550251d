# 🔧 API Reference - Ultimate 8X Strategy

## 📋 Table of Contents
1. [Class Overview](#class-overview)
2. [Core Methods](#core-methods)
3. [Configuration](#configuration)
4. [Data Structures](#data-structures)
5. [Usage Examples](#usage-examples)
6. [Error Handling](#error-handling)

## 🏗️ Class Overview

### Ultimate8xStrategy

The main strategy class that implements the adaptive 8x trading algorithm.

```typescript
class Ultimate8xStrategy {
  constructor(initialBalance: number = 100)
}
```

**Parameters:**
- `initialBalance` (number): Starting balance in USDT (default: 100)

## 🔧 Core Methods

### Data Management

#### `loadData(filePath: string): Promise<void>`
Loads historical market data from CSV file.

**Parameters:**
- `filePath` (string): Path to CSV file with columns: time,open,high,low,close,volume

**Example:**
```typescript
await strategy.loadData("./data/historical_data_mar_15m.csv");
```

#### `resetForNewDataset(): void`
Resets strategy state for testing on new dataset.

### Strategy Execution

#### `runBacktest(datasetName: string): Promise<BacktestResult>`
Executes the complete backtest on loaded data.

**Parameters:**
- `datasetName` (string): Name identifier for the dataset

**Returns:**
- `Promise<BacktestResult>`: Complete backtest results

**Example:**
```typescript
const result = await strategy.runBacktest("March 2025");
```

#### `testUltimate8xStrategy(): Promise<void>`
Runs comprehensive testing across multiple datasets.

### Market Analysis

#### `calculateMarketState(index: number): MarketState`
Determines current market regime based on volatility, momentum, and volume.

**Parameters:**
- `index` (number): Current candle index

**Returns:**
- `MarketState`: Object containing regime classification and metrics

#### `adaptToMarketState(state: MarketState): void`
Adjusts strategy parameters based on detected market regime.

**Parameters:**
- `state` (MarketState): Current market state

### Trading Logic

#### `shouldEnterLong(candle, indicators, prevCandle, state, index): {enter: boolean, reason: string}`
Determines if long position should be opened.

**Parameters:**
- `candle` (CandleData): Current candle
- `indicators` (object): Technical indicators
- `prevCandle` (CandleData): Previous candle
- `state` (MarketState): Current market state
- `index` (number): Current index

**Returns:**
- Object with entry decision and reason

#### `shouldEnterShort(candle, indicators, prevCandle, state, index): {enter: boolean, reason: string}`
Determines if short position should be opened.

#### `shouldExitPosition(candle, indicators, state): {exit: boolean, reason: string}`
Determines if current position should be closed.

### Position Management

#### `calculatePositionSize(state: MarketState): number`
Calculates optimal position size based on market regime and account balance.

**Parameters:**
- `state` (MarketState): Current market state

**Returns:**
- `number`: Position size in USDT

#### `openPosition(side, price, time, reason, state): void`
Opens a new trading position.

**Parameters:**
- `side` ("LONG" | "SHORT"): Position direction
- `price` (number): Entry price
- `time` (string): Entry timestamp
- `reason` (string): Entry reason
- `state` (MarketState): Market state

#### `closePosition(price, time, reason): void`
Closes current position.

**Parameters:**
- `price` (number): Exit price
- `time` (string): Exit timestamp
- `reason` (string): Exit reason

## ⚙️ Configuration

### Strategy Parameters

#### Base Parameters
```typescript
private readonly RSI_PERIOD = 14;
private readonly EMA_FAST = 8;
private readonly EMA_SLOW = 21;
private readonly SMA_PERIOD = 50;
private readonly VOLATILITY_PERIOD = 20;
```

#### Adaptive Parameters (change based on market regime)
```typescript
private RSI_OVERSOLD = 10;           // 5-25 based on regime
private RSI_OVERBOUGHT = 90;         // 75-95 based on regime
private LEVERAGE = 75;               // 40-150 based on regime
private RISK_PER_TRADE = 0.50;       // 0.30-0.80 based on regime
private STOP_LOSS_PERCENT = 0.005;   // 0.004-0.015 based on regime
private TAKE_PROFIT_PERCENT = 0.40;  // 0.10-0.50 based on regime
private VOLUME_MULTIPLIER = 2.0;     // 1.5-4.0 based on regime
private MOMENTUM_THRESHOLD = 0.010;  // 0.004-0.012 based on regime
```

### Regime-Specific Configurations

#### EXPLOSIVE Markets
```typescript
RSI_OVERSOLD = 5;
RSI_OVERBOUGHT = 95;
LEVERAGE = 100;
RISK_PER_TRADE = 0.60;
STOP_LOSS_PERCENT = 0.004;
TAKE_PROFIT_PERCENT = 0.50;
```

#### DEAD Markets
```typescript
RSI_OVERSOLD = 25;
RSI_OVERBOUGHT = 75;
LEVERAGE = 40;
RISK_PER_TRADE = 0.30;
STOP_LOSS_PERCENT = 0.015;
TAKE_PROFIT_PERCENT = 0.10;
```

## 📊 Data Structures

### CandleData
```typescript
interface CandleData {
  time: string;      // ISO timestamp
  open: number;      // Opening price
  high: number;      // Highest price
  low: number;       // Lowest price
  close: number;     // Closing price
  volume: number;    // Trading volume
}
```

### Position
```typescript
interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;              // Position size in USDT
  entryPrice: number;        // Entry price
  entryTime: string;         // Entry timestamp
  leverage: number;          // Applied leverage
  highestPrice?: number;     // Highest price reached (for trailing stops)
  lowestPrice?: number;      // Lowest price reached (for trailing stops)
  entryReason: string;       // Reason for entry
}
```

### Trade
```typescript
interface Trade {
  entryTime: string;         // Entry timestamp
  exitTime: string;          // Exit timestamp
  side: "LONG" | "SHORT";    // Position direction
  entryPrice: number;        // Entry price
  exitPrice: number;         // Exit price
  size: number;              // Position size
  pnl: number;               // Profit/Loss in USDT
  pnlPercent: number;        // Profit/Loss percentage
  leverage: number;          // Applied leverage
  entryReason: string;       // Entry reason
  exitReason: string;        // Exit reason
}
```

### MarketState
```typescript
interface MarketState {
  volatility: number;        // ATR-based volatility
  momentum: number;          // Price momentum
  volume: number;            // Volume ratio
  trend: number;             // Trend strength
  regime: "EXPLOSIVE" | "TRENDING" | "CHOPPY" | "DEAD";
}
```

### BacktestResult
```typescript
interface BacktestResult {
  dataset: string;           // Dataset name
  initialBalance: number;    // Starting balance
  finalBalance: number;      // Ending balance
  totalReturn: number;       // Total return in USDT
  totalReturnPercent: number;// Total return percentage
  totalTrades: number;       // Number of trades
  winningTrades: number;     // Number of winning trades
  losingTrades: number;      // Number of losing trades
  winRate: number;           // Win rate percentage
  maxDrawdown: number;       // Maximum drawdown percentage
  sharpeRatio: number;       // Sharpe ratio
  trades: Trade[];           // Array of all trades
  avgWin: number;            // Average winning trade
  avgLoss: number;           // Average losing trade
  profitFactor: number;      // Profit factor
  maxConsecutiveLosses: number;
  maxConsecutiveWins: number;
}
```

## 💡 Usage Examples

### Basic Usage
```typescript
import { Ultimate8xStrategy } from './ultimate_8x_strategy';

async function runStrategy() {
  const strategy = new Ultimate8xStrategy(100);
  
  await strategy.loadData('./data/historical_data_mar_15m.csv');
  const result = await strategy.runBacktest('March 2025');
  
  console.log(`Final Balance: ${result.finalBalance} USDT`);
  console.log(`Return: ${result.totalReturnPercent}%`);
  console.log(`Win Rate: ${result.winRate}%`);
}
```

### Multiple Dataset Testing
```typescript
async function testMultipleDatasets() {
  const strategy = new Ultimate8xStrategy(100);
  
  const datasets = [
    { name: "March 2025", path: "./data/historical_data_mar_15m.csv" },
    { name: "June 2025", path: "./data/historical_data_jun_15m.csv" }
  ];
  
  for (const dataset of datasets) {
    strategy.resetForNewDataset();
    await strategy.loadData(dataset.path);
    const result = await strategy.runBacktest(dataset.name);
    
    console.log(`${dataset.name}: ${result.finalBalance} USDT`);
  }
}
```

### Custom Configuration
```typescript
class CustomStrategy extends Ultimate8xStrategy {
  constructor(initialBalance: number) {
    super(initialBalance);
    
    // Override default parameters
    this.LEVERAGE = 50;  // More conservative leverage
    this.RISK_PER_TRADE = 0.25;  // Lower risk per trade
  }
}
```

## ⚠️ Error Handling

### Common Errors

#### Data Loading Errors
```typescript
try {
  await strategy.loadData('./invalid/path.csv');
} catch (error) {
  console.error('Failed to load data:', error.message);
}
```

#### Invalid CSV Format
- Ensure CSV has columns: time,open,high,low,close,volume
- Check for missing or invalid data points
- Verify timestamp format

#### Insufficient Data
- Minimum 100 candles required for indicators
- Check data quality and completeness

### Error Prevention

#### Data Validation
```typescript
private validateData(): boolean {
  if (this.data.length < 100) {
    throw new Error('Insufficient data: minimum 100 candles required');
  }
  
  for (const candle of this.data) {
    if (!candle.time || !candle.close || !candle.volume) {
      throw new Error('Invalid candle data');
    }
  }
  
  return true;
}
```

#### Balance Protection
```typescript
// Emergency exit if balance drops too low
if (this.balance < this.initialBalance * 0.03) {
  console.log("⚠️ EMERGENCY EXIT: Balance dropped below 3%");
  this.closeAllPositions();
}
```

## 🔧 Advanced Configuration

### Custom Market Regime Detection
```typescript
private customRegimeDetection(volatility: number, volume: number): string {
  // Implement custom logic
  if (volatility > 0.05 && volume > 3.0) {
    return "SUPER_EXPLOSIVE";
  }
  // ... other conditions
}
```

### Custom Entry Patterns
```typescript
private customEntryPattern(candle: CandleData, indicators: any): boolean {
  // Implement custom entry logic
  return candle.close > indicators.sma && 
         indicators.rsi < 30 && 
         candle.volume > prevCandle.volume * 2;
}
```

---

**This API reference provides complete documentation for implementing and customizing the Ultimate 8X Strategy.**
