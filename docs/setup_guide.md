# 🚀 Quick Setup Guide - Separate API Keys

## 📋 Overview

Your trading bot now supports **separate API keys** for testnet and mainnet, making it much safer to test and deploy.

## 🔧 Configuration Structure

```env
# Testnet API Keys (for safe testing)
TESTNET_API_KEY=your_testnet_key
TESTNET_API_SECRET=your_testnet_secret

# Mainnet API Keys (for real trading)
MAINNET_API_KEY=your_mainnet_key
MAINNET_API_SECRET=your_mainnet_secret

# Network Toggle
USE_TESTNET=true  # Switch between testnet/mainnet
```

## 🎯 Step-by-Step Setup

### Step 1: Get Testnet API Keys
1. Visit: https://testnet.binancefuture.com/
2. Login with your Binance account
3. Go to API Management
4. Create new API key
5. Copy to `TESTNET_API_KEY` and `TESTNET_API_SECRET`

### Step 2: Test Configuration
```bash
npm run test-config
```

### Step 3: Start with Testnet
```bash
# Make sure USE_TESTNET=true
npm run bot
```

### Step 4: Get Mainnet API Keys (when ready)
1. Visit: https://www.binance.com/en/my/settings/api-management
2. Create new API key
3. Enable trading permissions
4. Copy to `MAINNET_API_KEY` and `MAINNET_API_SECRET`

### Step 5: Switch to Mainnet (when ready)
```bash
# Set USE_TESTNET=false in .env
./start_bot.sh  # Includes safety warnings
```

## 🛡️ Safety Features

- **Automatic Key Selection**: Bot uses correct keys based on `USE_TESTNET` setting
- **Validation**: Checks for placeholder values and missing keys
- **Network Detection**: Startup script warns about testnet vs mainnet
- **Progressive Testing**: Demo → Testnet Paper → Testnet Live → Mainnet Live

## 🔍 Verification Commands

```bash
# Test your configuration
npm run test-config

# Run demo (no API keys needed)
npm run demo

# Start bot with safety checks
./start_bot.sh
```

## ✅ Current Status

Your bot is configured for:
- ✅ **Testnet API Keys**: Set and ready
- ❌ **Mainnet API Keys**: Need to be configured
- ✅ **Testnet Mode**: Currently active
- ✅ **Paper Trading**: Safe mode enabled

## 🎉 Ready to Use!

1. **Test Now**: `npm run bot` (uses testnet keys)
2. **View Dashboard**: http://localhost:3000
3. **Add Mainnet Keys**: When ready for real trading
4. **Switch Networks**: Change `USE_TESTNET=false`

The bot will automatically use the correct API keys based on your network selection!
