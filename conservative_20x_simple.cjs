const fs = require('fs');
const csv = require('csv-parser');
const { RSI, EMA, SMA } = require('technicalindicators');

class Conservative20xStrategy {
  constructor(initialBalance = 40) {
    this.data = [];
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "" };
    this.trades = [];
    this.cycles = [];
    this.currentCycle = 1;
    this.cycleStartTime = "";
    this.cycleStartBalance = initialBalance;
    this.totalWithdrawn = 0;
    this.maxBalance = initialBalance;
    this.maxDrawdown = 0;

    // Conservative parameters
    this.RSI_OVERSOLD = 25;
    this.RSI_OVERBOUGHT = 75;
    this.LEVERAGE = 25;
    this.POSITION_SIZE_PERCENT = 0.5; // 50% of current balance
    this.STOP_LOSS_PERCENT = 0.015; // 1.5%
    this.TAKE_PROFIT_PERCENT = 0.045; // 4.5%
    this.VOLUME_MULTIPLIER = 1.8;
    this.MOMENTUM_THRESHOLD = 0.008;
    this.CYCLE_DURATION_DAYS = 30;
    this.WITHDRAWAL_FREQUENCY_DAYS = 14;
    this.KEEP_BALANCE = 40;

    console.log("🏦 CONSERVATIVE 20X STRATEGY WITH PROFIT WITHDRAWAL");
    console.log(`💰 Initial Balance: ${initialBalance} USDT`);
    console.log(`📊 Position Size: 50% of current balance`);
    console.log(`⏰ Cycle Duration: ${this.CYCLE_DURATION_DAYS} days`);
    console.log(`💸 Withdrawal Check: Every ${this.WITHDRAWAL_FREQUENCY_DAYS} days`);
    console.log(`🔒 Keep Balance: ${this.KEEP_BALANCE} USDT`);
  }

  async loadData(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on('end', () => {
          this.data = results;
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          if (results.length > 0) {
            this.cycleStartTime = results[0].time;
          }
          resolve();
        })
        .on('error', reject);
    });
  }

  shouldWithdrawProfits(currentTime) {
    const currentDate = new Date(currentTime);
    const cycleStartDate = new Date(this.cycleStartTime);
    const daysDiff = (currentDate.getTime() - cycleStartDate.getTime()) / (1000 * 60 * 60 * 24);
    return daysDiff >= this.WITHDRAWAL_FREQUENCY_DAYS && this.balance > this.KEEP_BALANCE;
  }

  withdrawProfits(currentTime) {
    if (this.balance <= this.KEEP_BALANCE) return 0;
    
    const withdrawAmount = this.balance - this.KEEP_BALANCE;
    this.totalWithdrawn += withdrawAmount;
    this.balance = this.KEEP_BALANCE;
    
    console.log(`💸 PROFIT WITHDRAWAL at ${currentTime}`);
    console.log(`   Withdrawn: ${withdrawAmount.toFixed(2)} USDT`);
    console.log(`   Total Withdrawn: ${this.totalWithdrawn.toFixed(2)} USDT`);
    console.log(`   Remaining Balance: ${this.balance.toFixed(2)} USDT`);
    
    this.cycleStartTime = currentTime;
    return withdrawAmount;
  }

  calculatePositionSize() {
    return this.balance * this.POSITION_SIZE_PERCENT;
  }

  calculateIndicators(index) {
    const lookback = Math.min(100, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);
    const closes = candles.map(c => c.close);

    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 12, values: closes });
    const emaSlowValues = EMA.calculate({ period: 26, values: closes });
    const smaValues = SMA.calculate({ period: 50, values: closes });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast: emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      sma: smaValues[smaValues.length - 1] || closes[closes.length - 1],
    };
  }

  shouldEnterLong(candle, indicators, prevCandle, index) {
    const oversold = indicators.rsi < this.RSI_OVERSOLD;
    const emaGoldenCross = indicators.emaFast > indicators.emaSlow * 1.002;
    const aboveSMA = candle.close > indicators.sma;
    const greenCandle = candle.close > candle.open;
    const avgVolume = this.data.slice(Math.max(0, index - 10), index).reduce((sum, c) => sum + c.volume, 0) / 10;
    const goodVolume = candle.volume > avgVolume * this.VOLUME_MULTIPLIER;
    const momentum = Math.abs((candle.close - prevCandle.close) / prevCandle.close) > this.MOMENTUM_THRESHOLD;

    if (oversold && emaGoldenCross && aboveSMA && greenCandle && goodVolume) {
      return { enter: true, reason: "OVERSOLD_GOLDEN_CROSS_MOMENTUM" };
    }
    
    if (emaGoldenCross && momentum && goodVolume && greenCandle) {
      return { enter: true, reason: "EMA_CROSS_MOMENTUM" };
    }

    return { enter: false, reason: "NO_SIGNAL" };
  }

  shouldEnterShort(candle, indicators, prevCandle, index) {
    const overbought = indicators.rsi > this.RSI_OVERBOUGHT;
    const emaDeathCross = indicators.emaFast < indicators.emaSlow * 0.998;
    const belowSMA = candle.close < indicators.sma;
    const redCandle = candle.close < candle.open;
    const avgVolume = this.data.slice(Math.max(0, index - 10), index).reduce((sum, c) => sum + c.volume, 0) / 10;
    const goodVolume = candle.volume > avgVolume * this.VOLUME_MULTIPLIER;
    const momentum = Math.abs((candle.close - prevCandle.close) / prevCandle.close) > this.MOMENTUM_THRESHOLD;

    if (overbought && emaDeathCross && belowSMA && redCandle && goodVolume) {
      return { enter: true, reason: "OVERBOUGHT_DEATH_CROSS_MOMENTUM" };
    }
    
    if (emaDeathCross && momentum && goodVolume && redCandle) {
      return { enter: true, reason: "EMA_DEATH_MOMENTUM" };
    }

    return { enter: false, reason: "NO_SIGNAL" };
  }

  shouldExit(candle, indicators) {
    if (!this.position.side) return { exit: false, reason: "NO_POSITION" };

    const currentPrice = candle.close;
    const entryPrice = this.position.entryPrice;
    
    if (this.position.side === "LONG") {
      const stopLoss = entryPrice * (1 - this.STOP_LOSS_PERCENT);
      const takeProfit = entryPrice * (1 + this.TAKE_PROFIT_PERCENT);
      
      if (currentPrice <= stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice >= takeProfit) return { exit: true, reason: "TAKE_PROFIT" };
      if (indicators.rsi > this.RSI_OVERBOUGHT) return { exit: true, reason: "RSI_OVERBOUGHT" };
    } else {
      const stopLoss = entryPrice * (1 + this.STOP_LOSS_PERCENT);
      const takeProfit = entryPrice * (1 - this.TAKE_PROFIT_PERCENT);
      
      if (currentPrice >= stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice <= takeProfit) return { exit: true, reason: "TAKE_PROFIT" };
      if (indicators.rsi < this.RSI_OVERSOLD) return { exit: true, reason: "RSI_OVERSOLD" };
    }

    return { exit: false, reason: "HOLD" };
  }

  openPosition(side, candle, reason) {
    const positionSize = this.calculatePositionSize();
    const leverage = this.LEVERAGE;
    const contractSize = (positionSize * leverage) / candle.close;

    this.position = {
      side,
      size: contractSize,
      entryPrice: candle.close,
      entryTime: candle.time,
      leverage,
      entryReason: reason,
    };

    console.log(`🟢 OPEN ${side} at ${candle.close} | Size: ${positionSize.toFixed(2)} USDT (${leverage}x) | Balance: ${this.balance.toFixed(2)} | ${reason}`);
  }

  closePosition(candle, reason) {
    if (!this.position.side) return;

    const exitPrice = candle.close;
    const entryPrice = this.position.entryPrice;
    const size = this.position.size;

    let pnl = 0;
    if (this.position.side === "LONG") {
      pnl = size * (exitPrice - entryPrice);
    } else {
      pnl = size * (entryPrice - exitPrice);
    }

    const pnlPercent = (pnl / (this.balance * this.POSITION_SIZE_PERCENT)) * 100;
    const balanceBefore = this.balance;
    this.balance += pnl;

    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }
    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    const trade = {
      side: this.position.side,
      entryPrice,
      exitPrice,
      entryTime: this.position.entryTime,
      exitTime: candle.time,
      size,
      pnl,
      pnlPercent,
      reason,
      entryReason: this.position.entryReason,
      leverage: this.position.leverage,
      balanceBefore,
      balanceAfter: this.balance,
      cycle: this.currentCycle,
    };

    this.trades.push(trade);

    const pnlColor = pnl > 0 ? "🟢" : "🔴";
    console.log(`${pnlColor} CLOSE ${this.position.side} | PnL: ${pnl.toFixed(2)} (${pnlPercent.toFixed(2)}%) | Balance: ${this.balance.toFixed(2)} | ${reason}`);

    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "" };
  }

  async runBacktest() {
    console.log(`\n🚀 Starting Conservative 20X Strategy Backtest`);
    console.log(`📊 Data points: ${this.data.length}`);
    console.log(`⏰ Period: ${this.data[0]?.time} → ${this.data[this.data.length - 1]?.time}`);

    for (let i = 50; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      // Check for profit withdrawal
      if (this.shouldWithdrawProfits(candle.time)) {
        this.withdrawProfits(candle.time);
      }

      // Exit logic
      if (this.position.side) {
        const exitSignal = this.shouldExit(candle, indicators);
        if (exitSignal.exit) {
          this.closePosition(candle, exitSignal.reason);
        }
      }

      // Entry logic
      if (!this.position.side && this.balance >= 10) {
        const longSignal = this.shouldEnterLong(candle, indicators, prevCandle, i);
        const shortSignal = this.shouldEnterShort(candle, indicators, prevCandle, i);

        if (longSignal.enter) {
          this.openPosition("LONG", candle, longSignal.reason);
        } else if (shortSignal.enter) {
          this.openPosition("SHORT", candle, shortSignal.reason);
        }
      }
    }

    if (this.position.side) {
      this.closePosition(this.data[this.data.length - 1], "END_OF_DATA");
    }

    this.printResults();
  }

  printResults() {
    const finalBalance = this.balance;
    const totalReturn = finalBalance - this.initialBalance + this.totalWithdrawn;
    const returnPercent = ((totalReturn) / this.initialBalance) * 100;
    const returnMultiplier = (this.initialBalance + totalReturn) / this.initialBalance;

    const winningTrades = this.trades.filter(t => t.pnl > 0);
    const losingTrades = this.trades.filter(t => t.pnl <= 0);
    const winRate = (winningTrades.length / this.trades.length) * 100;
    const avgWin = winningTrades.length > 0 ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length : 0;
    const profitFactor = Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : 0;

    console.log("\n" + "=".repeat(80));
    console.log("🏦 CONSERVATIVE 20X STRATEGY - FINAL RESULTS");
    console.log("=".repeat(80));
    console.log(`💰 Initial Balance: ${this.initialBalance.toFixed(2)} USDT`);
    console.log(`💰 Final Balance: ${finalBalance.toFixed(2)} USDT`);
    console.log(`💸 Total Withdrawn: ${this.totalWithdrawn.toFixed(2)} USDT`);
    console.log(`💎 Total Value: ${(finalBalance + this.totalWithdrawn).toFixed(2)} USDT`);
    console.log(`📈 Total Return: ${totalReturn.toFixed(2)} USDT (${returnPercent.toFixed(2)}%)`);
    console.log(`🚀 Return Multiplier: ${returnMultiplier.toFixed(2)}x`);
    console.log(`🎯 Target Achieved (21x): ${returnMultiplier >= 21 ? '✅ YES' : '❌ NO'}`);
    console.log(`📊 Total Trades: ${this.trades.length}`);
    console.log(`🏆 Win Rate: ${winRate.toFixed(2)}%`);
    console.log(`📉 Max Drawdown: ${(this.maxDrawdown * 100).toFixed(2)}%`);
    console.log(`💎 Profit Factor: ${profitFactor.toFixed(2)}`);

    // Save results
    const result = {
      strategy: "Conservative 20X Strategy with Profit Withdrawal",
      initialBalance: this.initialBalance,
      finalBalance,
      totalWithdrawn: this.totalWithdrawn,
      totalValue: finalBalance + this.totalWithdrawn,
      totalReturn,
      returnPercent,
      returnMultiplier,
      maxBalance: this.maxBalance,
      maxDrawdown: this.maxDrawdown * 100,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,
      avgWin,
      avgLoss,
      profitFactor,
      targetAchieved: returnMultiplier >= 21,
      trades: this.trades,
      dataStart: this.data[0]?.time,
      dataEnd: this.data[this.data.length - 1]?.time,
    };

    const resultsFile = `./results/backtests/conservative_20x_${Date.now()}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to: ${resultsFile}`);
  }
}

// Run the strategy
async function main() {
  const strategy = new Conservative20xStrategy(40);
  
  try {
    const dataFile = './data/history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv';
    console.log(`🚀 Running Conservative 20X Strategy on: ${dataFile}`);
    
    await strategy.loadData(dataFile);
    await strategy.runBacktest();
  } catch (error) {
    console.error('❌ Error running backtest:', error);
  }
}

main().catch(console.error);
