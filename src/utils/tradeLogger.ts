import fs from 'fs';
import path from 'path';

export interface TradeLog {
  timestamp: string;
  type: 'OPEN' | 'CLOSE' | 'INFO' | 'ERROR' | 'WARNING';
  symbol: string;
  side?: 'LONG' | 'SHORT';
  price?: number;
  quantity?: number;
  pnl?: number;
  pnlPercent?: number;
  balance?: number;
  reason?: string;
  message: string;
  orderId?: string;
  leverage?: number;
  marketState?: string;
}

export class TradeLogger {
  private logFile: string;
  private webLogFile: string;
  private logs: TradeLog[] = [];
  private maxLogs = 1000; // Keep last 1000 logs in memory

  constructor() {
    const logsDir = './logs';
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    const today = new Date().toISOString().split('T')[0];
    this.logFile = path.join(logsDir, `trades_${today}.log`);
    this.webLogFile = path.join(logsDir, 'web_trades.json');
  }

  log(logEntry: Omit<TradeLog, 'timestamp'>): void {
    const timestamp = new Date().toISOString();
    const fullLogEntry: TradeLog = {
      timestamp,
      ...logEntry
    };

    // Add to memory
    this.logs.push(fullLogEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Console log with colors
    this.logToConsole(fullLogEntry);

    // Write to file
    this.logToFile(fullLogEntry);

    // Update web log
    this.updateWebLog();
  }

  private logToConsole(log: TradeLog): void {
    const colors = {
      OPEN: '\x1b[32m', // Green
      CLOSE: '\x1b[31m', // Red
      INFO: '\x1b[36m', // Cyan
      ERROR: '\x1b[91m', // Bright Red
      WARNING: '\x1b[33m', // Yellow
      RESET: '\x1b[0m'
    };

    const color = colors[log.type] || colors.INFO;
    const prefix = `${color}[${log.timestamp}] [${log.type}]${colors.RESET}`;
    
    let message = `${prefix} ${log.message}`;
    
    if (log.type === 'OPEN' || log.type === 'CLOSE') {
      message += ` | ${log.symbol} ${log.side} @ ${log.price}`;
      if (log.quantity) message += ` | Qty: ${log.quantity}`;
      if (log.pnl !== undefined) message += ` | PnL: ${log.pnl.toFixed(2)} USDT (${log.pnlPercent?.toFixed(2)}%)`;
      if (log.balance) message += ` | Balance: ${log.balance.toFixed(2)} USDT`;
      if (log.reason) message += ` | Reason: ${log.reason}`;
    }

    console.log(message);
  }

  private logToFile(log: TradeLog): void {
    const logLine = JSON.stringify(log) + '\n';
    fs.appendFileSync(this.logFile, logLine);
  }

  private updateWebLog(): void {
    // Keep only last 100 logs for web display
    const webLogs = this.logs.slice(-100);
    fs.writeFileSync(this.webLogFile, JSON.stringify(webLogs, null, 2));
  }

  openPosition(symbol: string, side: 'LONG' | 'SHORT', price: number, quantity: number, 
               reason: string, balance: number, leverage: number, marketState: string, orderId?: string): void {
    this.log({
      type: 'OPEN',
      symbol,
      side,
      price,
      quantity,
      balance,
      reason,
      leverage,
      marketState,
      orderId,
      message: `🟢 OPENED ${side} POSITION`
    });
  }

  closePosition(symbol: string, side: 'LONG' | 'SHORT', price: number, quantity: number,
                pnl: number, pnlPercent: number, balance: number, reason: string, orderId?: string): void {
    this.log({
      type: 'CLOSE',
      symbol,
      side,
      price,
      quantity,
      pnl,
      pnlPercent,
      balance,
      reason,
      orderId,
      message: `🔴 CLOSED ${side} POSITION`
    });
  }

  info(message: string, symbol?: string): void {
    this.log({
      type: 'INFO',
      symbol: symbol || 'SYSTEM',
      message: `ℹ️ ${message}`
    });
  }

  warning(message: string, symbol?: string): void {
    this.log({
      type: 'WARNING',
      symbol: symbol || 'SYSTEM',
      message: `⚠️ ${message}`
    });
  }

  error(message: string, error?: any, symbol?: string): void {
    const errorMessage = error ? `${message}: ${error.message || error}` : message;
    this.log({
      type: 'ERROR',
      symbol: symbol || 'SYSTEM',
      message: `❌ ${errorMessage}`
    });
  }

  getLogs(): TradeLog[] {
    return [...this.logs];
  }

  getWebLogsPath(): string {
    return this.webLogFile;
  }
}
