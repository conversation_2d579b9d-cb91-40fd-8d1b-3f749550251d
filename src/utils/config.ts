/**
 * Configuration management utility
 */

import * as dotenv from 'dotenv';
import { TradingParameters, MarketRegime } from '../types/trading.js';
import { AdaptiveParameters } from '../types/strategy.js';

// Load environment variables
dotenv.config();

export interface BinanceConfig {
  apiKey: string;
  apiSecret: string;
  testnet: boolean;
  httpFutures: string;
  wsFutures: string;
}

export interface TradingConfig {
  symbol: string;
  timeframe: string;
  initialBalance: number;
  enableRealTrading: boolean;
  maxDailyTrades: number;
  maxConcurrentPositions: number;
  emergencyStopLoss: number;
}

export interface LoggingConfig {
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  enableFileLogging: boolean;
  enableWebLogging: boolean;
  logDirectory: string;
  maxLogFiles: number;
  maxLogSize: string;
}

export interface WebServerConfig {
  enabled: boolean;
  port: number;
  host: string;
  staticPath: string;
}

export class Config {
  private static instance: Config;
  
  public binance: BinanceConfig;
  public trading: TradingConfig;
  public logging: LoggingConfig;
  public webServer: WebServerConfig;
  
  private constructor() {
    this.binance = this.loadBinanceConfig();
    this.trading = this.loadTradingConfig();
    this.logging = this.loadLoggingConfig();
    this.webServer = this.loadWebServerConfig();
  }
  
  public static getInstance(): Config {
    if (!Config.instance) {
      Config.instance = new Config();
    }
    return Config.instance;
  }
  
  private loadBinanceConfig(): BinanceConfig {
    const testnet = process.env.USE_TESTNET === 'true';
    
    return {
      apiKey: testnet ? 
        (process.env.BINANCE_TESTNET_API_KEY || '') : 
        (process.env.BINANCE_API_KEY || ''),
      apiSecret: testnet ? 
        (process.env.BINANCE_TESTNET_API_SECRET || '') : 
        (process.env.BINANCE_API_SECRET || ''),
      testnet,
      httpFutures: testnet ? 
        'https://testnet.binancefuture.com' : 
        'https://fapi.binance.com',
      wsFutures: testnet ? 
        'wss://stream.binancefuture.com' : 
        'wss://fstream.binance.com'
    };
  }
  
  private loadTradingConfig(): TradingConfig {
    return {
      symbol: process.env.TRADING_SYMBOL || 'BTCUSDT',
      timeframe: process.env.TIMEFRAME || '15m',
      initialBalance: parseFloat(process.env.INITIAL_BALANCE || '100'),
      enableRealTrading: process.env.ENABLE_REAL_TRADING === 'true',
      maxDailyTrades: parseInt(process.env.MAX_DAILY_TRADES || '10'),
      maxConcurrentPositions: parseInt(process.env.MAX_CONCURRENT_POSITIONS || '1'),
      emergencyStopLoss: parseFloat(process.env.EMERGENCY_STOP_LOSS || '0.05')
    };
  }
  
  private loadLoggingConfig(): LoggingConfig {
    return {
      level: (process.env.LOG_LEVEL as any) || 'INFO',
      enableFileLogging: process.env.ENABLE_FILE_LOGGING !== 'false',
      enableWebLogging: process.env.ENABLE_WEB_LOGGING !== 'false',
      logDirectory: process.env.LOG_DIRECTORY || './results/logs',
      maxLogFiles: parseInt(process.env.MAX_LOG_FILES || '30'),
      maxLogSize: process.env.MAX_LOG_SIZE || '10MB'
    };
  }
  
  private loadWebServerConfig(): WebServerConfig {
    return {
      enabled: process.env.WEB_SERVER_ENABLED !== 'false',
      port: parseInt(process.env.WEB_SERVER_PORT || '3000'),
      host: process.env.WEB_SERVER_HOST || 'localhost',
      staticPath: process.env.WEB_STATIC_PATH || './web'
    };
  }
  
  public validateConfig(): boolean {
    const errors: string[] = [];
    
    // Validate Binance config
    if (!this.binance.apiKey) {
      errors.push('Binance API key is required');
    }
    if (!this.binance.apiSecret) {
      errors.push('Binance API secret is required');
    }
    
    // Validate trading config
    if (this.trading.initialBalance <= 0) {
      errors.push('Initial balance must be greater than 0');
    }
    if (this.trading.maxDailyTrades <= 0) {
      errors.push('Max daily trades must be greater than 0');
    }
    
    if (errors.length > 0) {
      console.error('Configuration validation failed:');
      errors.forEach(error => console.error(`- ${error}`));
      return false;
    }
    
    return true;
  }
}

// Strategy-specific configurations
export const STRATEGY_CONFIGS = {
  ULTIMATE_8X: {
    name: 'Ultimate 8X Strategy',
    targetMultiplier: 8,
    adaptiveParameters: {
      EXPLOSIVE: {
        regime: 'EXPLOSIVE' as MarketRegime,
        leverage: 100,
        riskPerTrade: 0.60,
        stopLossPercent: 0.004,
        takeProfitPercent: 0.50,
        rsiOversold: 5,
        rsiOverbought: 95,
        minVolumeRatio: 3.0,
        confidenceThreshold: 0.8
      } as AdaptiveParameters,
      TRENDING: {
        regime: 'TRENDING' as MarketRegime,
        leverage: 75,
        riskPerTrade: 0.45,
        stopLossPercent: 0.008,
        takeProfitPercent: 0.30,
        rsiOversold: 15,
        rsiOverbought: 85,
        minVolumeRatio: 2.0,
        confidenceThreshold: 0.7
      } as AdaptiveParameters,
      CHOPPY: {
        regime: 'CHOPPY' as MarketRegime,
        leverage: 50,
        riskPerTrade: 0.35,
        stopLossPercent: 0.012,
        takeProfitPercent: 0.20,
        rsiOversold: 20,
        rsiOverbought: 80,
        minVolumeRatio: 1.5,
        confidenceThreshold: 0.6
      } as AdaptiveParameters,
      DEAD: {
        regime: 'DEAD' as MarketRegime,
        leverage: 40,
        riskPerTrade: 0.30,
        stopLossPercent: 0.015,
        takeProfitPercent: 0.10,
        rsiOversold: 25,
        rsiOverbought: 75,
        minVolumeRatio: 1.2,
        confidenceThreshold: 0.5
      } as AdaptiveParameters
    }
  },
  
  ULTIMATE_20X: {
    name: 'Ultimate 20X Strategy',
    targetMultiplier: 20,
    adaptiveParameters: {
      EXPLOSIVE: {
        regime: 'EXPLOSIVE' as MarketRegime,
        leverage: 125,
        riskPerTrade: 0.80,
        stopLossPercent: 0.003,
        takeProfitPercent: 0.60,
        rsiOversold: 3,
        rsiOverbought: 97,
        minVolumeRatio: 4.0,
        confidenceThreshold: 0.9
      } as AdaptiveParameters,
      TRENDING: {
        regime: 'TRENDING' as MarketRegime,
        leverage: 100,
        riskPerTrade: 0.60,
        stopLossPercent: 0.006,
        takeProfitPercent: 0.40,
        rsiOversold: 10,
        rsiOverbought: 90,
        minVolumeRatio: 2.5,
        confidenceThreshold: 0.8
      } as AdaptiveParameters,
      CHOPPY: {
        regime: 'CHOPPY' as MarketRegime,
        leverage: 75,
        riskPerTrade: 0.45,
        stopLossPercent: 0.010,
        takeProfitPercent: 0.25,
        rsiOversold: 15,
        rsiOverbought: 85,
        minVolumeRatio: 2.0,
        confidenceThreshold: 0.7
      } as AdaptiveParameters,
      DEAD: {
        regime: 'DEAD' as MarketRegime,
        leverage: 50,
        riskPerTrade: 0.35,
        stopLossPercent: 0.012,
        takeProfitPercent: 0.15,
        rsiOversold: 20,
        rsiOverbought: 80,
        minVolumeRatio: 1.5,
        confidenceThreshold: 0.6
      } as AdaptiveParameters
    }
  }
};

export default Config;
