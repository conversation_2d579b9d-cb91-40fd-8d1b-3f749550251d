/**
 * Enhanced logging utility with file and web logging support
 */

import * as fs from 'fs';
import * as path from 'path';
import { LogLevel } from '../types/trading.js';
import { Config } from './config.js';

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  category?: string;
}

export interface TradeLogEntry extends LogEntry {
  type: 'OPEN' | 'CLOSE' | 'INFO' | 'ERROR' | 'WARNING';
  symbol?: string;
  side?: 'LONG' | 'SHORT';
  price?: number;
  quantity?: number;
  pnl?: number;
  pnlPercent?: number;
  balance?: number;
  reason?: string;
}

export class Logger {
  private static instance: Logger;
  private config = Config.getInstance();
  private logs: LogEntry[] = [];
  private tradeLogs: TradeLogEntry[] = [];
  private maxLogs = 1000;
  private logFile: string;
  private tradeLogFile: string;
  private webLogFile: string;

  private constructor() {
    const today = new Date().toISOString().split('T')[0];
    this.logFile = path.join(this.config.logging.logDirectory, `app_${today}.log`);
    this.tradeLogFile = path.join(this.config.logging.logDirectory, `trades_${today}.log`);
    this.webLogFile = path.join(this.config.logging.logDirectory, 'web_trades.json');
    
    // Ensure log directory exists
    this.ensureLogDirectory();
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.config.logging.logDirectory)) {
      fs.mkdirSync(this.config.logging.logDirectory, { recursive: true });
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['DEBUG', 'INFO', 'WARN', 'ERROR'];
    const configLevel = levels.indexOf(this.config.logging.level);
    const messageLevel = levels.indexOf(level);
    return messageLevel >= configLevel;
  }

  private formatMessage(entry: LogEntry): string {
    const timestamp = entry.timestamp;
    const level = entry.level.padEnd(5);
    const category = entry.category ? `[${entry.category}]` : '';
    const data = entry.data ? ` | ${JSON.stringify(entry.data)}` : '';
    return `${timestamp} [${level}] ${category} ${entry.message}${data}`;
  }

  private logToConsole(entry: LogEntry): void {
    const colors = {
      DEBUG: '\x1b[36m',   // Cyan
      INFO: '\x1b[32m',    // Green
      WARN: '\x1b[33m',    // Yellow
      ERROR: '\x1b[31m',   // Red
      RESET: '\x1b[0m'
    };

    const color = colors[entry.level] || colors.INFO;
    const message = this.formatMessage(entry);
    console.log(`${color}${message}${colors.RESET}`);
  }

  private logToFile(entry: LogEntry): void {
    if (!this.config.logging.enableFileLogging) return;

    const message = this.formatMessage(entry) + '\n';
    fs.appendFileSync(this.logFile, message);
  }

  private updateWebLog(): void {
    if (!this.config.logging.enableWebLogging) return;

    // Keep only last 100 logs for web display
    const webLogs = this.tradeLogs.slice(-100);
    fs.writeFileSync(this.webLogFile, JSON.stringify(webLogs, null, 2));
  }

  public log(level: LogLevel, message: string, data?: any, category?: string): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      category
    };

    // Add to memory
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Output to console and file
    this.logToConsole(entry);
    this.logToFile(entry);
  }

  public debug(message: string, data?: any, category?: string): void {
    this.log('DEBUG', message, data, category);
  }

  public info(message: string, data?: any, category?: string): void {
    this.log('INFO', message, data, category);
  }

  public warn(message: string, data?: any, category?: string): void {
    this.log('WARN', message, data, category);
  }

  public error(message: string, data?: any, category?: string): void {
    this.log('ERROR', message, data, category);
  }

  // Trading-specific logging methods
  public logTrade(entry: Omit<TradeLogEntry, 'timestamp' | 'level'>): void {
    const timestamp = new Date().toISOString();
    const fullEntry: TradeLogEntry = {
      timestamp,
      level: 'INFO',
      ...entry
    };

    // Add to trade logs
    this.tradeLogs.push(fullEntry);
    if (this.tradeLogs.length > this.maxLogs) {
      this.tradeLogs.shift();
    }

    // Console log with colors
    this.logTradeToConsole(fullEntry);

    // Write to trade log file
    if (this.config.logging.enableFileLogging) {
      const logLine = JSON.stringify(fullEntry) + '\n';
      fs.appendFileSync(this.tradeLogFile, logLine);
    }

    // Update web log
    this.updateWebLog();
  }

  private logTradeToConsole(log: TradeLogEntry): void {
    const colors = {
      OPEN: '\x1b[32m',     // Green
      CLOSE: '\x1b[31m',    // Red
      INFO: '\x1b[36m',     // Cyan
      ERROR: '\x1b[91m',    // Bright Red
      WARNING: '\x1b[33m',  // Yellow
      RESET: '\x1b[0m'
    };

    const color = colors[log.type] || colors.INFO;
    const prefix = `${color}[${log.timestamp}] [${log.type}]${colors.RESET}`;
    
    let message = `${prefix} ${log.message}`;
    
    if (log.type === 'OPEN' || log.type === 'CLOSE') {
      if (log.symbol && log.side && log.price) {
        message += ` | ${log.symbol} ${log.side} @ ${log.price}`;
      }
      if (log.quantity) message += ` | Qty: ${log.quantity}`;
      if (log.pnl !== undefined) message += ` | PnL: ${log.pnl.toFixed(2)} USDT (${log.pnlPercent?.toFixed(2)}%)`;
      if (log.balance) message += ` | Balance: ${log.balance.toFixed(2)} USDT`;
      if (log.reason) message += ` | Reason: ${log.reason}`;
    }

    console.log(message);
  }

  public openPosition(symbol: string, side: 'LONG' | 'SHORT', price: number, quantity: number, 
                     reason: string, balance: number, leverage: number, marketState: string, orderId?: string): void {
    this.logTrade({
      type: 'OPEN',
      message: `Opening ${side} position`,
      symbol,
      side,
      price,
      quantity,
      balance,
      reason,
      data: { leverage, marketState, orderId }
    });
  }

  public closePosition(symbol: string, side: 'LONG' | 'SHORT', entryPrice: number, exitPrice: number,
                      quantity: number, pnl: number, pnlPercent: number, balance: number, 
                      reason: string, duration: number, orderId?: string): void {
    this.logTrade({
      type: 'CLOSE',
      message: `Closing ${side} position`,
      symbol,
      side,
      price: exitPrice,
      quantity,
      pnl,
      pnlPercent,
      balance,
      reason,
      data: { entryPrice, duration, orderId }
    });
  }

  public tradingInfo(message: string, data?: any): void {
    this.logTrade({
      type: 'INFO',
      message,
      data
    });
  }

  public tradingWarning(message: string, data?: any): void {
    this.logTrade({
      type: 'WARNING',
      message,
      data
    });
  }

  public tradingError(message: string, error?: any): void {
    this.logTrade({
      type: 'ERROR',
      message,
      data: error
    });
  }

  public getLogs(count: number = 100): LogEntry[] {
    return this.logs.slice(-count);
  }

  public getTradeLogs(count: number = 100): TradeLogEntry[] {
    return this.tradeLogs.slice(-count);
  }

  public clearLogs(): void {
    this.logs = [];
    this.tradeLogs = [];
  }
}

export default Logger;
