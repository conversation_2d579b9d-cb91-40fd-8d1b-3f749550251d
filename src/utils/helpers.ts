/**
 * Helper utility functions
 */

import * as fs from 'fs';
import * as path from 'path';
import { CandleData } from '../types/market.js';
import { Trade, BacktestResult } from '../types/trading.js';

export class Helpers {
  
  /**
   * Format number to specified decimal places
   */
  public static formatNumber(num: number, decimals: number = 2): string {
    return num.toFixed(decimals);
  }

  /**
   * Format percentage
   */
  public static formatPercent(num: number, decimals: number = 2): string {
    return `${(num * 100).toFixed(decimals)}%`;
  }

  /**
   * Format currency
   */
  public static formatCurrency(num: number, currency: string = 'USDT', decimals: number = 2): string {
    return `${num.toFixed(decimals)} ${currency}`;
  }

  /**
   * Calculate percentage change
   */
  public static calculatePercentChange(oldValue: number, newValue: number): number {
    if (oldValue === 0) return 0;
    return (newValue - oldValue) / oldValue;
  }

  /**
   * Calculate return multiplier
   */
  public static calculateMultiplier(initial: number, final: number): number {
    if (initial === 0) return 0;
    return final / initial;
  }

  /**
   * Sleep for specified milliseconds
   */
  public static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current timestamp
   */
  public static getCurrentTimestamp(): string {
    return new Date().toISOString();
  }

  /**
   * Format duration in human readable format
   */
  public static formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }

  /**
   * Validate file exists
   */
  public static fileExists(filePath: string): boolean {
    return fs.existsSync(filePath);
  }

  /**
   * Ensure directory exists
   */
  public static ensureDirectory(dirPath: string): void {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
  }

  /**
   * Save JSON data to file
   */
  public static saveJSON(data: any, filePath: string): void {
    this.ensureDirectory(path.dirname(filePath));
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  }

  /**
   * Load JSON data from file
   */
  public static loadJSON(filePath: string): any {
    if (!this.fileExists(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    const content = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(content);
  }

  /**
   * Calculate statistics for an array of numbers
   */
  public static calculateStats(numbers: number[]): {
    mean: number;
    median: number;
    min: number;
    max: number;
    stdDev: number;
    sum: number;
    count: number;
  } {
    if (numbers.length === 0) {
      return { mean: 0, median: 0, min: 0, max: 0, stdDev: 0, sum: 0, count: 0 };
    }

    const sorted = [...numbers].sort((a, b) => a - b);
    const sum = numbers.reduce((acc, val) => acc + val, 0);
    const mean = sum / numbers.length;
    const median = sorted.length % 2 === 0 
      ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
      : sorted[Math.floor(sorted.length / 2)];
    
    const variance = numbers.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / numbers.length;
    const stdDev = Math.sqrt(variance);

    return {
      mean,
      median,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      stdDev,
      sum,
      count: numbers.length
    };
  }

  /**
   * Calculate drawdown series
   */
  public static calculateDrawdownSeries(balances: number[]): number[] {
    const drawdowns: number[] = [];
    let peak = balances[0];

    for (const balance of balances) {
      if (balance > peak) {
        peak = balance;
      }
      const drawdown = (peak - balance) / peak;
      drawdowns.push(drawdown);
    }

    return drawdowns;
  }

  /**
   * Calculate maximum drawdown
   */
  public static calculateMaxDrawdown(balances: number[]): number {
    const drawdowns = this.calculateDrawdownSeries(balances);
    return Math.max(...drawdowns);
  }

  /**
   * Calculate Sharpe ratio
   */
  public static calculateSharpeRatio(returns: number[], riskFreeRate: number = 0): number {
    if (returns.length === 0) return 0;
    
    const stats = this.calculateStats(returns);
    const excessReturn = stats.mean - riskFreeRate;
    
    return stats.stdDev > 0 ? excessReturn / stats.stdDev : 0;
  }

  /**
   * Calculate profit factor
   */
  public static calculateProfitFactor(trades: Trade[]): number {
    const winningTrades = trades.filter(t => t.pnl > 0);
    const losingTrades = trades.filter(t => t.pnl < 0);
    
    const totalWins = winningTrades.reduce((sum, t) => sum + t.pnl, 0);
    const totalLosses = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0));
    
    return totalLosses > 0 ? totalWins / totalLosses : totalWins > 0 ? Infinity : 0;
  }

  /**
   * Generate trade summary
   */
  public static generateTradeSummary(trades: Trade[]): {
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
    winRate: number;
    avgWin: number;
    avgLoss: number;
    profitFactor: number;
    totalPnL: number;
    maxConsecutiveWins: number;
    maxConsecutiveLosses: number;
  } {
    if (trades.length === 0) {
      return {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        avgWin: 0,
        avgLoss: 0,
        profitFactor: 0,
        totalPnL: 0,
        maxConsecutiveWins: 0,
        maxConsecutiveLosses: 0
      };
    }

    const winningTrades = trades.filter(t => t.pnl > 0);
    const losingTrades = trades.filter(t => t.pnl < 0);
    
    const avgWin = winningTrades.length > 0 
      ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length 
      : 0;
    
    const avgLoss = losingTrades.length > 0 
      ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length 
      : 0;

    // Calculate consecutive wins/losses
    let maxConsecutiveWins = 0;
    let maxConsecutiveLosses = 0;
    let currentWins = 0;
    let currentLosses = 0;

    for (const trade of trades) {
      if (trade.pnl > 0) {
        currentWins++;
        currentLosses = 0;
        maxConsecutiveWins = Math.max(maxConsecutiveWins, currentWins);
      } else {
        currentLosses++;
        currentWins = 0;
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLosses);
      }
    }

    return {
      totalTrades: trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: (winningTrades.length / trades.length) * 100,
      avgWin,
      avgLoss,
      profitFactor: this.calculateProfitFactor(trades),
      totalPnL: trades.reduce((sum, t) => sum + t.pnl, 0),
      maxConsecutiveWins,
      maxConsecutiveLosses
    };
  }

  /**
   * Validate trading parameters
   */
  public static validateTradingParams(params: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (params.leverage <= 0 || params.leverage > 200) {
      errors.push('Leverage must be between 1 and 200');
    }

    if (params.riskPerTrade <= 0 || params.riskPerTrade > 1) {
      errors.push('Risk per trade must be between 0 and 1');
    }

    if (params.stopLossPercent <= 0 || params.stopLossPercent > 0.5) {
      errors.push('Stop loss percent must be between 0 and 0.5');
    }

    if (params.takeProfitPercent <= 0 || params.takeProfitPercent > 5) {
      errors.push('Take profit percent must be between 0 and 5');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate unique ID
   */
  public static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Clamp value between min and max
   */
  public static clamp(value: number, min: number, max: number): number {
    return Math.min(Math.max(value, min), max);
  }

  /**
   * Round to nearest step
   */
  public static roundToStep(value: number, step: number): number {
    return Math.round(value / step) * step;
  }

  /**
   * Check if value is within range
   */
  public static isInRange(value: number, min: number, max: number): boolean {
    return value >= min && value <= max;
  }
}

export default Helpers;
