#!/usr/bin/env node

import fs from "fs";
import csv from "csv-parser";

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: Date;
}

interface PeriodAnalysis {
  startDate: string;
  endDate: string;
  startPrice: number;
  endPrice: number;
  highPrice: number;
  lowPrice: number;
  priceChange: number;
  priceChangePercent: number;
  volatility: number;
  avgVolume: number;
  trendDirection: "UP" | "DOWN" | "SIDEWAYS";
  maxDrawdown: number;
  maxGain: number;
  tradingDays: number;
}

interface PatternSummary {
  period: string;
  totalPeriods: number;
  bullishPeriods: number;
  bearishPeriods: number;
  sidewaysPeriods: number;
  avgGain: number;
  avgLoss: number;
  maxGainPeriod: PeriodAnalysis;
  maxLossPeriod: PeriodAnalysis;
  patterns: string[];
}

class PatternAnalyzer {
  private data: CandleData[] = [];

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          const timestamp = new Date(data.time);
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
            timestamp
          });
        })
        .on("end", () => {
          this.data = results.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          console.log(`📅 Date range: ${this.data[0].time} to ${this.data[this.data.length - 1].time}`);
          resolve();
        })
        .on("error", reject);
    });
  }

  private calculateVolatility(prices: number[]): number {
    if (prices.length < 2) return 0;
    
    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }
    
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    
    return Math.sqrt(variance) * Math.sqrt(365); // Annualized volatility
  }

  private analyzePeriod(startIndex: number, endIndex: number): PeriodAnalysis {
    const periodData = this.data.slice(startIndex, endIndex + 1);
    const prices = periodData.map(d => d.close);
    const volumes = periodData.map(d => d.volume);
    
    const startPrice = periodData[0].close;
    const endPrice = periodData[periodData.length - 1].close;
    const highPrice = Math.max(...prices);
    const lowPrice = Math.min(...prices);
    
    const priceChange = endPrice - startPrice;
    const priceChangePercent = (priceChange / startPrice) * 100;
    
    // Calculate max drawdown and max gain from start
    let maxDrawdown = 0;
    let maxGain = 0;
    let peak = startPrice;
    
    for (const price of prices) {
      if (price > peak) peak = price;
      const drawdown = (peak - price) / peak;
      if (drawdown > maxDrawdown) maxDrawdown = drawdown;
      
      const gain = (price - startPrice) / startPrice;
      if (gain > maxGain) maxGain = gain;
    }
    
    const volatility = this.calculateVolatility(prices);
    const avgVolume = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
    
    let trendDirection: "UP" | "DOWN" | "SIDEWAYS";
    if (priceChangePercent > 5) trendDirection = "UP";
    else if (priceChangePercent < -5) trendDirection = "DOWN";
    else trendDirection = "SIDEWAYS";
    
    return {
      startDate: periodData[0].time,
      endDate: periodData[periodData.length - 1].time,
      startPrice,
      endPrice,
      highPrice,
      lowPrice,
      priceChange,
      priceChangePercent,
      volatility,
      avgVolume,
      trendDirection,
      maxDrawdown: maxDrawdown * 100,
      maxGain: maxGain * 100,
      tradingDays: periodData.length
    };
  }

  analyzeWeeklyPatterns(): PatternSummary {
    console.log("\n📅 ANALYZING WEEKLY PATTERNS");
    console.log("=" .repeat(60));
    
    const weeklyAnalyses: PeriodAnalysis[] = [];
    const candlesPerWeek = 7 * 24 * 4; // 7 days * 24 hours * 4 (15-min candles per hour)
    
    for (let i = 0; i < this.data.length - candlesPerWeek; i += candlesPerWeek) {
      const endIndex = Math.min(i + candlesPerWeek - 1, this.data.length - 1);
      const analysis = this.analyzePeriod(i, endIndex);
      weeklyAnalyses.push(analysis);
      
      console.log(`Week ${weeklyAnalyses.length}: ${analysis.startDate} to ${analysis.endDate}`);
      console.log(`  Price: ${analysis.startPrice.toFixed(2)} → ${analysis.endPrice.toFixed(2)} (${analysis.priceChangePercent.toFixed(2)}%)`);
      console.log(`  Range: ${analysis.lowPrice.toFixed(2)} - ${analysis.highPrice.toFixed(2)}`);
      console.log(`  Max Gain: ${analysis.maxGain.toFixed(2)}% | Max Drawdown: ${analysis.maxDrawdown.toFixed(2)}%`);
      console.log(`  Trend: ${analysis.trendDirection} | Volatility: ${analysis.volatility.toFixed(2)}`);
      console.log("");
    }
    
    return this.summarizePatterns("WEEKLY", weeklyAnalyses);
  }

  analyzeMonthlyPatterns(): PatternSummary {
    console.log("\n📅 ANALYZING MONTHLY PATTERNS");
    console.log("=" .repeat(60));
    
    const monthlyAnalyses: PeriodAnalysis[] = [];
    const candlesPerMonth = 30 * 24 * 4; // 30 days * 24 hours * 4 (15-min candles per hour)
    
    for (let i = 0; i < this.data.length - candlesPerMonth; i += candlesPerMonth) {
      const endIndex = Math.min(i + candlesPerMonth - 1, this.data.length - 1);
      const analysis = this.analyzePeriod(i, endIndex);
      monthlyAnalyses.push(analysis);
      
      console.log(`Month ${monthlyAnalyses.length}: ${analysis.startDate} to ${analysis.endDate}`);
      console.log(`  Price: ${analysis.startPrice.toFixed(2)} → ${analysis.endPrice.toFixed(2)} (${analysis.priceChangePercent.toFixed(2)}%)`);
      console.log(`  Range: ${analysis.lowPrice.toFixed(2)} - ${analysis.highPrice.toFixed(2)}`);
      console.log(`  Max Gain: ${analysis.maxGain.toFixed(2)}% | Max Drawdown: ${analysis.maxDrawdown.toFixed(2)}%`);
      console.log(`  Trend: ${analysis.trendDirection} | Volatility: ${analysis.volatility.toFixed(2)}`);
      console.log("");
    }
    
    return this.summarizePatterns("MONTHLY", monthlyAnalyses);
  }

  analyzeQuarterlyPatterns(): PatternSummary {
    console.log("\n📅 ANALYZING QUARTERLY PATTERNS");
    console.log("=" .repeat(60));
    
    const quarterlyAnalyses: PeriodAnalysis[] = [];
    const candlesPerQuarter = 90 * 24 * 4; // 90 days * 24 hours * 4 (15-min candles per hour)
    
    for (let i = 0; i < this.data.length - candlesPerQuarter; i += candlesPerQuarter) {
      const endIndex = Math.min(i + candlesPerQuarter - 1, this.data.length - 1);
      const analysis = this.analyzePeriod(i, endIndex);
      quarterlyAnalyses.push(analysis);
      
      console.log(`Quarter ${quarterlyAnalyses.length}: ${analysis.startDate} to ${analysis.endDate}`);
      console.log(`  Price: ${analysis.startPrice.toFixed(2)} → ${analysis.endPrice.toFixed(2)} (${analysis.priceChangePercent.toFixed(2)}%)`);
      console.log(`  Range: ${analysis.lowPrice.toFixed(2)} - ${analysis.highPrice.toFixed(2)}`);
      console.log(`  Max Gain: ${analysis.maxGain.toFixed(2)}% | Max Drawdown: ${analysis.maxDrawdown.toFixed(2)}%`);
      console.log(`  Trend: ${analysis.trendDirection} | Volatility: ${analysis.volatility.toFixed(2)}`);
      console.log("");
    }
    
    return this.summarizePatterns("QUARTERLY", quarterlyAnalyses);
  }

  private summarizePatterns(period: string, analyses: PeriodAnalysis[]): PatternSummary {
    const bullishPeriods = analyses.filter(a => a.trendDirection === "UP").length;
    const bearishPeriods = analyses.filter(a => a.trendDirection === "DOWN").length;
    const sidewaysPeriods = analyses.filter(a => a.trendDirection === "SIDEWAYS").length;
    
    const gains = analyses.filter(a => a.priceChangePercent > 0);
    const losses = analyses.filter(a => a.priceChangePercent < 0);
    
    const avgGain = gains.length > 0 ? gains.reduce((sum, a) => sum + a.priceChangePercent, 0) / gains.length : 0;
    const avgLoss = losses.length > 0 ? losses.reduce((sum, a) => sum + a.priceChangePercent, 0) / losses.length : 0;
    
    const maxGainPeriod = analyses.reduce((max, current) => 
      current.priceChangePercent > max.priceChangePercent ? current : max
    );
    
    const maxLossPeriod = analyses.reduce((min, current) => 
      current.priceChangePercent < min.priceChangePercent ? current : min
    );
    
    // Identify patterns
    const patterns: string[] = [];
    
    // Volatility patterns
    const avgVolatility = analyses.reduce((sum, a) => sum + a.volatility, 0) / analyses.length;
    if (avgVolatility > 1.0) patterns.push("HIGH_VOLATILITY");
    else if (avgVolatility < 0.5) patterns.push("LOW_VOLATILITY");
    else patterns.push("MODERATE_VOLATILITY");
    
    // Trend patterns
    if (bullishPeriods > bearishPeriods * 1.5) patterns.push("BULLISH_DOMINANT");
    else if (bearishPeriods > bullishPeriods * 1.5) patterns.push("BEARISH_DOMINANT");
    else patterns.push("MIXED_TRENDS");
    
    // Gain/Loss patterns
    if (Math.abs(avgGain) > Math.abs(avgLoss) * 1.5) patterns.push("STRONG_UPSIDE");
    else if (Math.abs(avgLoss) > Math.abs(avgGain) * 1.5) patterns.push("STRONG_DOWNSIDE");
    else patterns.push("BALANCED_MOVES");
    
    // Max gain analysis for 20x potential
    if (maxGainPeriod.maxGain > 2000) patterns.push("20X_POTENTIAL_DETECTED");
    else if (maxGainPeriod.maxGain > 1000) patterns.push("10X_POTENTIAL_DETECTED");
    else if (maxGainPeriod.maxGain > 500) patterns.push("5X_POTENTIAL_DETECTED");
    else patterns.push("LIMITED_GAIN_POTENTIAL");
    
    return {
      period,
      totalPeriods: analyses.length,
      bullishPeriods,
      bearishPeriods,
      sidewaysPeriods,
      avgGain,
      avgLoss,
      maxGainPeriod,
      maxLossPeriod,
      patterns
    };
  }

  printPatternSummary(summary: PatternSummary): void {
    console.log(`\n🔍 ${summary.period} PATTERN SUMMARY`);
    console.log("=" .repeat(60));
    console.log(`📊 Total Periods: ${summary.totalPeriods}`);
    console.log(`📈 Bullish: ${summary.bullishPeriods} (${(summary.bullishPeriods/summary.totalPeriods*100).toFixed(1)}%)`);
    console.log(`📉 Bearish: ${summary.bearishPeriods} (${(summary.bearishPeriods/summary.totalPeriods*100).toFixed(1)}%)`);
    console.log(`➡️  Sideways: ${summary.sidewaysPeriods} (${(summary.sidewaysPeriods/summary.totalPeriods*100).toFixed(1)}%)`);
    console.log(`💰 Average Gain: ${summary.avgGain.toFixed(2)}%`);
    console.log(`💸 Average Loss: ${summary.avgLoss.toFixed(2)}%`);
    
    console.log(`\n🏆 BEST PERIOD:`);
    console.log(`   ${summary.maxGainPeriod.startDate} to ${summary.maxGainPeriod.endDate}`);
    console.log(`   Gain: ${summary.maxGainPeriod.priceChangePercent.toFixed(2)}% | Max Gain: ${summary.maxGainPeriod.maxGain.toFixed(2)}%`);
    
    console.log(`\n📉 WORST PERIOD:`);
    console.log(`   ${summary.maxLossPeriod.startDate} to ${summary.maxLossPeriod.endDate}`);
    console.log(`   Loss: ${summary.maxLossPeriod.priceChangePercent.toFixed(2)}% | Max Drawdown: ${summary.maxLossPeriod.maxDrawdown.toFixed(2)}%`);
    
    console.log(`\n🔍 IDENTIFIED PATTERNS:`);
    summary.patterns.forEach(pattern => {
      console.log(`   • ${pattern}`);
    });
  }

  async analyzeAllPatterns(dataFile: string): Promise<void> {
    console.log("🔍 COMPREHENSIVE PATTERN ANALYSIS");
    console.log("Analyzing historical data to find 20x trading opportunities");
    console.log("=" .repeat(80));
    
    await this.loadData(dataFile);
    
    // Analyze different time periods
    const weeklyPatterns = this.analyzeWeeklyPatterns();
    const monthlyPatterns = this.analyzeMonthlyPatterns();
    const quarterlyPatterns = this.analyzeQuarterlyPatterns();
    
    // Print summaries
    this.printPatternSummary(weeklyPatterns);
    this.printPatternSummary(monthlyPatterns);
    this.printPatternSummary(quarterlyPatterns);
    
    // Generate trading logic based on patterns
    this.generateTradingLogic(weeklyPatterns, monthlyPatterns, quarterlyPatterns);
    
    // Save analysis
    const analysis = {
      timestamp: new Date().toISOString(),
      dataFile,
      weeklyPatterns,
      monthlyPatterns,
      quarterlyPatterns
    };
    
    fs.writeFileSync("pattern_analysis.json", JSON.stringify(analysis, null, 2));
    console.log("\n💾 Pattern analysis saved to pattern_analysis.json");
  }

  private generateTradingLogic(weekly: PatternSummary, monthly: PatternSummary, quarterly: PatternSummary): void {
    console.log("\n🎯 TRADING LOGIC FOR 20X RETURNS");
    console.log("=" .repeat(80));
    
    // Analyze the best performing periods
    const bestMonthlyGain = monthly.maxGainPeriod.maxGain;
    const bestWeeklyGain = weekly.maxGainPeriod.maxGain;
    
    console.log(`📊 MAXIMUM GAINS DETECTED:`);
    console.log(`   Best Monthly Max Gain: ${bestMonthlyGain.toFixed(2)}%`);
    console.log(`   Best Weekly Max Gain: ${bestWeeklyGain.toFixed(2)}%`);
    
    if (bestMonthlyGain >= 2000) {
      console.log(`\n🎉 20X POTENTIAL CONFIRMED!`);
      console.log(`   The data shows ${bestMonthlyGain.toFixed(2)}% max gain in one month`);
      console.log(`   This exceeds the 2000% needed for 20x returns!`);
    } else {
      console.log(`\n⚠️  20X CHALLENGING:`);
      console.log(`   Max monthly gain: ${bestMonthlyGain.toFixed(2)}% (need 2000% for 20x)`);
      console.log(`   Gap to target: ${(2000 - bestMonthlyGain).toFixed(2)}%`);
    }
    
    // Generate specific trading recommendations
    console.log(`\n💡 RECOMMENDED TRADING STRATEGY:`);
    
    if (weekly.patterns.includes("HIGH_VOLATILITY")) {
      console.log(`   ✅ High volatility detected - Use scalping strategy`);
      console.log(`   ✅ Entry: Quick RSI reversals with high volume`);
      console.log(`   ✅ Exit: 10-20% profits, tight 3-5% stops`);
    }
    
    if (monthly.patterns.includes("BULLISH_DOMINANT")) {
      console.log(`   ✅ Bullish trend dominant - Use trend following`);
      console.log(`   ✅ Entry: Pullbacks to support levels`);
      console.log(`   ✅ Exit: Ride trends for 50-100% gains`);
    }
    
    if (bestMonthlyGain > 500) {
      console.log(`   ✅ Large moves possible - Use breakout strategy`);
      console.log(`   ✅ Entry: Volume breakouts above resistance`);
      console.log(`   ✅ Exit: Trail stops to capture full moves`);
    }
    
    // Calculate required trade frequency for 20x
    const avgMonthlyGain = monthly.avgGain;
    const tradesNeeded = Math.ceil(2000 / avgMonthlyGain);
    
    console.log(`\n📈 MATHEMATICAL ANALYSIS FOR 20X:`);
    console.log(`   Average monthly gain: ${avgMonthlyGain.toFixed(2)}%`);
    console.log(`   Trades needed for 20x: ${tradesNeeded} consecutive wins`);
    console.log(`   Win rate required: ${(100/tradesNeeded).toFixed(1)}% minimum`);
    
    if (tradesNeeded <= 10) {
      console.log(`   ✅ ACHIEVABLE: ${tradesNeeded} wins is realistic`);
    } else {
      console.log(`   ⚠️  CHALLENGING: ${tradesNeeded} consecutive wins is difficult`);
    }
  }
}

// Run pattern analysis
async function main() {
  const analyzer = new PatternAnalyzer();
  
  // Get first available data file
  const dataDir = "./data";
  const files = fs.readdirSync(dataDir).filter(file => file.endsWith('.csv'));
  
  if (files.length === 0) {
    console.error("❌ No CSV data files found");
    return;
  }
  
  const dataFile = `${dataDir}/${files[0]}`;
  await analyzer.analyzeAllPatterns(dataFile);
}

main().catch(console.error);
