/**
 * Technical indicators utility functions
 */

import { RSI, EMA, SMA, MACD, BollingerBands, ATR, ADX, Stochastic } from 'technicalindicators';
import { CandleData, TechnicalIndicators } from '../types/market.js';

export class IndicatorCalculator {
  
  /**
   * Calculate RSI (Relative Strength Index)
   */
  public static calculateRSI(closes: number[], period: number = 14): number[] {
    if (closes.length < period) return [];
    return RSI.calculate({ values: closes, period });
  }

  /**
   * Calculate EMA (Exponential Moving Average)
   */
  public static calculateEMA(closes: number[], period: number): number[] {
    if (closes.length < period) return [];
    return EMA.calculate({ values: closes, period });
  }

  /**
   * Calculate SMA (Simple Moving Average)
   */
  public static calculateSMA(closes: number[], period: number): number[] {
    if (closes.length < period) return [];
    return SMA.calculate({ values: closes, period });
  }

  /**
   * Calculate MACD (Moving Average Convergence Divergence)
   */
  public static calculateMACD(closes: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {
    if (closes.length < slowPeriod) return [];
    return MACD.calculate({
      values: closes,
      fastPeriod,
      slowPeriod,
      signalPeriod,
      SimpleMAOscillator: false,
      SimpleMASignal: false
    });
  }

  /**
   * Calculate Bollinger Bands
   */
  public static calculateBollingerBands(closes: number[], period: number = 20, stdDev: number = 2) {
    if (closes.length < period) return [];
    return BollingerBands.calculate({
      values: closes,
      period,
      stdDev
    });
  }

  /**
   * Calculate ATR (Average True Range)
   */
  public static calculateATR(candles: CandleData[], period: number = 14) {
    if (candles.length < period) return [];
    
    const input = candles.map(candle => ({
      high: candle.high,
      low: candle.low,
      close: candle.close
    }));
    
    return ATR.calculate({ high: input.map(c => c.high), low: input.map(c => c.low), close: input.map(c => c.close), period });
  }

  /**
   * Calculate ADX (Average Directional Index)
   */
  public static calculateADX(candles: CandleData[], period: number = 14) {
    if (candles.length < period) return [];
    
    return ADX.calculate({
      high: candles.map(c => c.high),
      low: candles.map(c => c.low),
      close: candles.map(c => c.close),
      period
    });
  }

  /**
   * Calculate Stochastic Oscillator
   */
  public static calculateStochastic(candles: CandleData[], kPeriod: number = 14, dPeriod: number = 3) {
    if (candles.length < kPeriod) return [];
    
    return Stochastic.calculate({
      high: candles.map(c => c.high),
      low: candles.map(c => c.low),
      close: candles.map(c => c.close),
      period: kPeriod,
      signalPeriod: dPeriod
    });
  }

  /**
   * Calculate all indicators for a dataset
   */
  public static calculateAllIndicators(candles: CandleData[], index: number): TechnicalIndicators | null {
    if (index < 50 || candles.length < 50) return null; // Need enough data
    
    const closes = candles.slice(0, index + 1).map(c => c.close);
    const volumes = candles.slice(0, index + 1).map(c => c.volume);
    const candleSlice = candles.slice(0, index + 1);
    
    try {
      // RSI
      const rsiValues = this.calculateRSI(closes, 14);
      const rsi = rsiValues.length > 0 ? rsiValues[rsiValues.length - 1] : 50;
      
      // EMAs
      const emaFastValues = this.calculateEMA(closes, 12);
      const emaSlowValues = this.calculateEMA(closes, 26);
      const emaFast = emaFastValues.length > 0 ? emaFastValues[emaFastValues.length - 1] : closes[closes.length - 1];
      const emaSlow = emaSlowValues.length > 0 ? emaSlowValues[emaSlowValues.length - 1] : closes[closes.length - 1];
      
      // SMA
      const smaValues = this.calculateSMA(closes, 20);
      const sma = smaValues.length > 0 ? smaValues[smaValues.length - 1] : closes[closes.length - 1];
      
      // MACD
      const macdValues = this.calculateMACD(closes);
      const macd = macdValues.length > 0 ? macdValues[macdValues.length - 1] : { MACD: 0, signal: 0, histogram: 0 };
      
      // Bollinger Bands
      const bbValues = this.calculateBollingerBands(closes);
      const bb = bbValues.length > 0 ? bbValues[bbValues.length - 1] : { upper: closes[closes.length - 1], middle: closes[closes.length - 1], lower: closes[closes.length - 1] };
      
      // ATR
      const atrValues = this.calculateATR(candleSlice);
      const atr = atrValues.length > 0 ? atrValues[atrValues.length - 1] : 0;
      
      // ADX
      const adxValues = this.calculateADX(candleSlice);
      const adx = adxValues.length > 0 ? adxValues[adxValues.length - 1] : 0;
      
      // Stochastic
      const stochValues = this.calculateStochastic(candleSlice);
      const stoch = stochValues.length > 0 ? stochValues[stochValues.length - 1] : { k: 50, d: 50 };
      
      // Volume analysis
      const recentVolumes = volumes.slice(-20);
      const avgVolume = recentVolumes.reduce((sum, vol) => sum + vol, 0) / recentVolumes.length;
      const currentVolume = volumes[volumes.length - 1];
      const volumeRatio = avgVolume > 0 ? currentVolume / avgVolume : 1;
      
      return {
        rsi,
        emaFast,
        emaSlow,
        sma,
        macd: macd.MACD || 0,
        macdSignal: macd.signal || 0,
        macdHistogram: macd.histogram || 0,
        bb_upper: bb.upper,
        bb_middle: bb.middle,
        bb_lower: bb.lower,
        atr,
        adx,
        stochK: stoch.k,
        stochD: stoch.d,
        volume: currentVolume,
        avgVolume,
        volumeRatio
      };
      
    } catch (error) {
      console.error('Error calculating indicators:', error);
      return null;
    }
  }

  /**
   * Calculate volatility
   */
  public static calculateVolatility(closes: number[], period: number = 20): number {
    if (closes.length < period) return 0;
    
    const recentCloses = closes.slice(-period);
    const returns = [];
    
    for (let i = 1; i < recentCloses.length; i++) {
      const returnValue = (recentCloses[i] - recentCloses[i - 1]) / recentCloses[i - 1];
      returns.push(returnValue);
    }
    
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    
    return Math.sqrt(variance) * Math.sqrt(365); // Annualized volatility
  }

  /**
   * Detect price patterns
   */
  public static detectPatterns(candles: CandleData[], index: number): string[] {
    if (index < 5) return [];
    
    const patterns: string[] = [];
    const recent = candles.slice(Math.max(0, index - 4), index + 1);
    
    if (recent.length < 5) return patterns;
    
    // Hammer pattern
    if (this.isHammer(recent[recent.length - 1])) {
      patterns.push('HAMMER');
    }
    
    // Doji pattern
    if (this.isDoji(recent[recent.length - 1])) {
      patterns.push('DOJI');
    }
    
    // Engulfing patterns
    if (recent.length >= 2) {
      if (this.isBullishEngulfing(recent[recent.length - 2], recent[recent.length - 1])) {
        patterns.push('BULLISH_ENGULFING');
      }
      if (this.isBearishEngulfing(recent[recent.length - 2], recent[recent.length - 1])) {
        patterns.push('BEARISH_ENGULFING');
      }
    }
    
    return patterns;
  }

  private static isHammer(candle: CandleData): boolean {
    const body = Math.abs(candle.close - candle.open);
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
    const upperShadow = candle.high - Math.max(candle.open, candle.close);
    
    return lowerShadow > body * 2 && upperShadow < body * 0.5;
  }

  private static isDoji(candle: CandleData): boolean {
    const body = Math.abs(candle.close - candle.open);
    const range = candle.high - candle.low;
    
    return body < range * 0.1;
  }

  private static isBullishEngulfing(prev: CandleData, current: CandleData): boolean {
    return prev.close < prev.open && // Previous candle is bearish
           current.close > current.open && // Current candle is bullish
           current.open < prev.close && // Current opens below previous close
           current.close > prev.open; // Current closes above previous open
  }

  private static isBearishEngulfing(prev: CandleData, current: CandleData): boolean {
    return prev.close > prev.open && // Previous candle is bullish
           current.close < current.open && // Current candle is bearish
           current.open > prev.close && // Current opens above previous close
           current.close < prev.open; // Current closes below previous open
  }
}

export default IndicatorCalculator;
