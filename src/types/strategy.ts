/**
 * Strategy-related types and interfaces
 */

import { CandleData, TechnicalIndicators, MarketState } from './market.js';
import { Position, Trade, TradingParameters, BacktestResult } from './trading.js';

export interface StrategyConfig {
  name: string;
  version: string;
  description: string;
  parameters: TradingParameters;
  riskManagement: RiskManagementConfig;
  entryRules: EntryRule[];
  exitRules: ExitRule[];
}

export interface RiskManagementConfig {
  maxPositionSize: number;
  maxDailyLoss: number;
  maxDrawdown: number;
  stopLossType: 'FIXED' | 'TRAILING' | 'ATR' | 'DYNAMIC';
  takeProfitType: 'FIXED' | 'TRAILING' | 'RATIO' | 'DYNAMIC';
  positionSizing: 'FIXED' | 'KELLY' | 'PERCENT_RISK' | 'VOLATILITY';
}

export interface EntryRule {
  name: string;
  type: 'TECHNICAL' | 'FUNDAMENTAL' | 'SENTIMENT' | 'PATTERN';
  condition: string;
  weight: number;
  required: boolean;
}

export interface ExitRule {
  name: string;
  type: 'STOP_LOSS' | 'TAKE_PROFIT' | 'TRAILING_STOP' | 'TIME_BASED' | 'SIGNAL_BASED';
  condition: string;
  priority: number;
}

export interface StrategySignal {
  timestamp: string;
  action: 'BUY' | 'SELL' | 'HOLD';
  strength: number;
  confidence: number;
  reasons: string[];
  indicators: TechnicalIndicators;
  marketState: MarketState;
  riskReward: number;
  stopLoss?: number;
  takeProfit?: number;
}

export interface StrategyPerformance {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  profitFactor: number;
  sharpeRatio: number;
  maxDrawdown: number;
  avgWin: number;
  avgLoss: number;
  totalReturn: number;
  annualizedReturn: number;
  volatility: number;
  calmarRatio: number;
}

export interface StrategyState {
  isActive: boolean;
  currentPosition: Position | null;
  balance: number;
  equity: number;
  unrealizedPnL: number;
  dailyPnL: number;
  totalTrades: number;
  lastTradeTime: string;
  marketRegime: MarketState['regime'];
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'EXTREME';
}

export interface BaseStrategy {
  name: string;
  config: StrategyConfig;
  state: StrategyState;
  
  // Core methods
  initialize(config: StrategyConfig): Promise<void>;
  analyze(candle: CandleData, indicators: TechnicalIndicators): Promise<StrategySignal>;
  shouldEnter(signal: StrategySignal): boolean;
  shouldExit(signal: StrategySignal, position: Position): boolean;
  calculatePositionSize(signal: StrategySignal, balance: number): number;
  updateState(trade?: Trade): void;
  
  // Backtesting methods
  backtest(data: CandleData[], initialBalance: number): Promise<BacktestResult>;
  
  // Risk management
  validateRisk(signal: StrategySignal, position: Position | null): boolean;
  calculateStopLoss(entryPrice: number, signal: StrategySignal): number;
  calculateTakeProfit(entryPrice: number, signal: StrategySignal): number;
}

export interface AdaptiveParameters {
  regime: MarketState['regime'];
  leverage: number;
  riskPerTrade: number;
  stopLossPercent: number;
  takeProfitPercent: number;
  rsiOversold: number;
  rsiOverbought: number;
  minVolumeRatio: number;
  confidenceThreshold: number;
}

export interface StrategyOptimization {
  parameter: string;
  currentValue: number;
  testRange: [number, number];
  step: number;
  bestValue: number;
  bestPerformance: number;
  optimizationMetric: 'RETURN' | 'SHARPE' | 'PROFIT_FACTOR' | 'WIN_RATE';
}

export interface StrategyComparison {
  strategies: string[];
  metrics: {
    [strategyName: string]: StrategyPerformance;
  };
  winner: string;
  ranking: string[];
  summary: string;
}
