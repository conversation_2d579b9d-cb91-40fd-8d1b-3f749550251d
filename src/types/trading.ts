/**
 * Core trading types and interfaces
 */

export interface Position {
  side: 'LONG' | 'SHORT' | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  entryReason: string;
  stopLoss?: number;
  takeProfit?: number;
  trailingStop?: number;
  orderId?: string;
}

export interface Trade {
  id: string;
  symbol: string;
  side: 'LONG' | 'SHORT';
  entryPrice: number;
  exitPrice: number;
  quantity: number;
  leverage: number;
  entryTime: string;
  exitTime: string;
  entryReason: string;
  exitReason: string;
  pnl: number;
  pnlPercent: number;
  fee: number;
  duration: number;
  balanceBefore: number;
  balanceAfter: number;
}

export interface OrderSignal {
  enter: boolean;
  exit: boolean;
  reason: string;
  confidence?: number;
  stopLoss?: number;
  takeProfit?: number;
}

export interface TradingParameters {
  leverage: number;
  riskPerTrade: number;
  stopLossPercent: number;
  takeProfitPercent: number;
  rsiOversold: number;
  rsiOverbought: number;
  minVolume: number;
  maxDrawdown: number;
}

export interface AccountInfo {
  balance: number;
  availableBalance: number;
  totalWalletBalance: number;
  totalUnrealizedProfit: number;
  totalMarginBalance: number;
  totalPositionInitialMargin: number;
  totalOpenOrderInitialMargin: number;
  maxWithdrawAmount: number;
  canTrade: boolean;
  canDeposit: boolean;
  canWithdraw: boolean;
}

export interface OrderResult {
  orderId: string;
  symbol: string;
  status: string;
  clientOrderId: string;
  price: string;
  avgPrice: string;
  origQty: string;
  executedQty: string;
  cumQuote: string;
  timeInForce: string;
  type: string;
  reduceOnly: boolean;
  closePosition: boolean;
  side: string;
  positionSide: string;
  stopPrice: string;
  workingType: string;
  priceProtect: boolean;
  origType: string;
  updateTime: number;
}

export interface RiskMetrics {
  maxDrawdown: number;
  sharpeRatio: number;
  profitFactor: number;
  winRate: number;
  avgWin: number;
  avgLoss: number;
  maxConsecutiveLosses: number;
  maxConsecutiveWins: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
}

export interface BacktestResult {
  strategy: string;
  symbol: string;
  timeframe: string;
  initialBalance: number;
  finalBalance: number;
  totalReturn: number;
  returnPercent: number;
  returnMultiplier: number;
  maxBalance: number;
  maxDrawdown: number;
  trades: Trade[];
  riskMetrics: RiskMetrics;
  targetAchieved: boolean;
  dataStart: string;
  dataEnd: string;
  duration: number;
}

export type MarketRegime = 'EXPLOSIVE' | 'TRENDING' | 'CHOPPY' | 'DEAD';
export type TradingMode = 'LIVE' | 'PAPER' | 'BACKTEST';
export type LogLevel = 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
