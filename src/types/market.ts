/**
 * Market data types and interfaces
 */

export interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp?: number;
}

export interface TickerData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  high: number;
  low: number;
  timestamp: number;
}

export interface OrderBookEntry {
  price: number;
  quantity: number;
}

export interface OrderBook {
  symbol: string;
  bids: OrderBookEntry[];
  asks: OrderBookEntry[];
  timestamp: number;
}

export interface TechnicalIndicators {
  rsi: number;
  emaFast: number;
  emaSlow: number;
  sma: number;
  macd: number;
  macdSignal: number;
  macdHistogram: number;
  bb_upper: number;
  bb_middle: number;
  bb_lower: number;
  atr: number;
  adx: number;
  stochK: number;
  stochD: number;
  volume: number;
  avgVolume: number;
  volumeRatio: number;
}

export interface MarketState {
  regime: 'EXPLOSIVE' | 'TRENDING' | 'CHOPPY' | 'DEAD';
  volatility: number;
  trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';
  strength: number;
  volume: 'HIGH' | 'MEDIUM' | 'LOW';
  momentum: number;
  confidence: number;
}

export interface PumpSignal {
  time: string;
  entryPrice: number;
  pumpStartPrice: number;
  pumpEndPrice: number;
  maxGain: number;
  duration: number;
  signals: string[];
  rsi: number;
  volume: number;
  volumeRatio: number;
  priceAction: any;
}

export interface MarketAnalysis {
  currentPrice: number;
  priceChange24h: number;
  priceChangePercent24h: number;
  volume24h: number;
  volumeChange24h: number;
  marketCap?: number;
  dominance?: number;
  fearGreedIndex?: number;
  technicalIndicators: TechnicalIndicators;
  marketState: MarketState;
  signals: {
    buy: boolean;
    sell: boolean;
    strength: number;
    reasons: string[];
  };
}

export interface DatasetInfo {
  name: string;
  path: string;
  startDate: string;
  endDate: string;
  totalCandles: number;
  timeframe: string;
  symbol: string;
}

export interface VolatilityMetrics {
  current: number;
  average: number;
  percentile: number;
  trend: 'INCREASING' | 'DECREASING' | 'STABLE';
}

export interface VolumeMetrics {
  current: number;
  average: number;
  ratio: number;
  trend: 'INCREASING' | 'DECREASING' | 'STABLE';
  spike: boolean;
}
