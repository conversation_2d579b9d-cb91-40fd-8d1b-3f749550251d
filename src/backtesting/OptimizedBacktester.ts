import csv from "csv-parser";
import fs from "fs";
import { EMA, RSI, SMA } from "technicalindicators";

// Types for our trading system
interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  highestPrice?: number;
  lowestPrice?: number;
}

interface Trade {
  entryTime: string;
  exitTime: string;
  side: "LONG" | "SHORT";
  entryPrice: number;
  exitPrice: number;
  size: number;
  pnl: number;
  pnlPercent: number;
  leverage: number;
}

interface BacktestResult {
  initialBalance: number;
  finalBalance: number;
  totalReturn: number;
  totalReturnPercent: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Trade[];
}

class OptimizedFuturesBacktester {
  private data: CandleData[] = [];
  private balance: number;
  private initialBalance: number;
  private position: Position = {
    side: null,
    size: 0,
    entryPrice: 0,
    entryTime: "",
    leverage: 1,
  };
  private trades: Trade[] = [];
  private maxBalance: number;
  private maxDrawdown: number = 0;

  // ULTIMATE STRATEGY PARAMETERS for 8x target
  private readonly RSI_PERIOD = 14;
  private readonly EMA_FAST = 8;
  private readonly EMA_SLOW = 21;
  private readonly SMA_PERIOD = 50;
  private readonly RSI_OVERSOLD = 5; // EXTREME oversold
  private readonly RSI_OVERBOUGHT = 95; // EXTREME overbought
  private readonly LEVERAGE = 50; // MAXIMUM leverage
  private readonly RISK_PER_TRADE = 0.4; // 40% risk per trade
  private readonly STOP_LOSS_PERCENT = 0.006; // 0.6% ultra tight stop
  private readonly TAKE_PROFIT_PERCENT = 0.3; // 30% take profit (50:1 R:R)

  constructor(initialBalance: number = 100) {
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.maxBalance = initialBalance;
  }

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          console.log(`Loaded ${results.length} candles from ${filePath}`);
          resolve();
        })
        .on("error", reject);
    });
  }

  private calculateIndicators(
    index: number
  ): {
    rsi: number;
    emaFast: number;
    emaSlow: number;
    sma: number;
  } | null {
    if (index < Math.max(this.RSI_PERIOD, this.EMA_SLOW, this.SMA_PERIOD)) {
      return null;
    }

    const closes = this.data.slice(0, index + 1).map((d) => d.close);

    const rsiValues = RSI.calculate({
      values: closes,
      period: this.RSI_PERIOD,
    });
    const emaFastValues = EMA.calculate({
      values: closes,
      period: this.EMA_FAST,
    });
    const emaSlowValues = EMA.calculate({
      values: closes,
      period: this.EMA_SLOW,
    });
    const smaValues = SMA.calculate({
      values: closes,
      period: this.SMA_PERIOD,
    });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 0,
      emaFast: emaFastValues[emaFastValues.length - 1] || 0,
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || 0,
      sma: smaValues[smaValues.length - 1] || 0,
    };
  }

  private shouldEnterLong(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData
  ): boolean {
    // ULTIMATE MOMENTUM HUNTER STRATEGY

    // 1. EXTREME CONDITIONS
    const extremeOversold = indicators.rsi < this.RSI_OVERSOLD;
    const rsiRecovering = indicators.rsi > 3 && indicators.rsi < 20;

    // 2. EXPLOSIVE MOMENTUM
    const explosiveGreenCandle =
      candle.close > candle.open &&
      (candle.close - candle.open) / candle.open > 0.008; // 0.8%
    const ultraVolume = candle.volume > prevCandle.volume * 5.0; // 5x volume
    const ultraMomentum = candle.close > prevCandle.close * 1.01; // 1% momentum

    // 3. TREND POWER
    const bullishTrend = indicators.emaFast > indicators.emaSlow;
    const strongBullishTrend =
      indicators.emaFast > indicators.emaSlow &&
      indicators.emaSlow > indicators.sma;
    const breakoutAboveSMA =
      candle.close > indicators.sma && prevCandle.close <= indicators.sma;
    const emaGap =
      (indicators.emaFast - indicators.emaSlow) / indicators.emaSlow > 0.003;

    // 4. PATTERN POWER
    const hammerPattern =
      candle.close - candle.low > 4 * Math.abs(candle.close - candle.open) &&
      candle.close > candle.open;
    const engulfingPattern =
      candle.close > prevCandle.high && candle.open < prevCandle.close;
    const gapUp = candle.low > prevCandle.high;
    const priceNearHigh = candle.close > candle.high * 0.999;

    // ULTIMATE ENTRY CONDITIONS
    return (
      // TIER 1: EXPLOSIVE MOMENTUM
      (explosiveGreenCandle && ultraVolume && strongBullishTrend) ||
      (ultraMomentum && ultraVolume && bullishTrend && indicators.rsi < 30) ||
      // TIER 2: EXTREME REVERSALS
      (extremeOversold && explosiveGreenCandle && ultraVolume) ||
      (rsiRecovering && engulfingPattern && ultraVolume && emaGap) ||
      // TIER 3: BREAKOUT POWER
      (breakoutAboveSMA && ultraMomentum && ultraVolume) ||
      (gapUp && explosiveGreenCandle && ultraVolume) ||
      // TIER 4: PATTERN CONFIRMATIONS
      (hammerPattern && indicators.rsi < 15 && ultraVolume) ||
      (priceNearHigh &&
        explosiveGreenCandle &&
        ultraVolume &&
        strongBullishTrend)
    );
  }

  private shouldEnterShort(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData
  ): boolean {
    // ULTIMATE MOMENTUM HUNTER STRATEGY

    // 1. EXTREME CONDITIONS
    const extremeOverbought = indicators.rsi > this.RSI_OVERBOUGHT;
    const rsiWeakening = indicators.rsi < 97 && indicators.rsi > 80;

    // 2. EXPLOSIVE MOMENTUM
    const explosiveRedCandle =
      candle.close < candle.open &&
      (candle.open - candle.close) / candle.open > 0.008; // 0.8%
    const ultraVolume = candle.volume > prevCandle.volume * 5.0; // 5x volume
    const ultraMomentum = candle.close < prevCandle.close * 0.99; // 1% downward momentum

    // 3. TREND POWER
    const bearishTrend = indicators.emaFast < indicators.emaSlow;
    const strongBearishTrend =
      indicators.emaFast < indicators.emaSlow &&
      indicators.emaSlow < indicators.sma;
    const breakdownBelowSMA =
      candle.close < indicators.sma && prevCandle.close >= indicators.sma;
    const emaGap =
      (indicators.emaSlow - indicators.emaFast) / indicators.emaFast > 0.003;

    // 4. PATTERN POWER
    const shootingStarPattern =
      candle.high - candle.close > 4 * Math.abs(candle.close - candle.open) &&
      candle.close < candle.open;
    const bearishEngulfing =
      candle.close < prevCandle.low && candle.open > prevCandle.close;
    const gapDown = candle.high < prevCandle.low;
    const priceNearLow = candle.close < candle.low * 1.001;

    // ULTIMATE ENTRY CONDITIONS
    return (
      // TIER 1: EXPLOSIVE MOMENTUM
      (explosiveRedCandle && ultraVolume && strongBearishTrend) ||
      (ultraMomentum && ultraVolume && bearishTrend && indicators.rsi > 70) ||
      // TIER 2: EXTREME REVERSALS
      (extremeOverbought && explosiveRedCandle && ultraVolume) ||
      (rsiWeakening && bearishEngulfing && ultraVolume && emaGap) ||
      // TIER 3: BREAKDOWN POWER
      (breakdownBelowSMA && ultraMomentum && ultraVolume) ||
      (gapDown && explosiveRedCandle && ultraVolume) ||
      // TIER 4: PATTERN CONFIRMATIONS
      (shootingStarPattern && indicators.rsi > 85 && ultraVolume) ||
      (priceNearLow && explosiveRedCandle && ultraVolume && strongBearishTrend)
    );
  }

  private shouldExitPosition(candle: CandleData, indicators: any): boolean {
    if (!this.position.side) return false;

    const currentPrice = candle.close;
    const entryPrice = this.position.entryPrice;

    // Update highest/lowest prices for trailing stop
    if (this.position.side === "LONG") {
      if (
        !this.position.highestPrice ||
        currentPrice > this.position.highestPrice
      ) {
        this.position.highestPrice = currentPrice;
      }
    } else {
      if (
        !this.position.lowestPrice ||
        currentPrice < this.position.lowestPrice
      ) {
        this.position.lowestPrice = currentPrice;
      }
    }

    const pnlPercent =
      this.position.side === "LONG"
        ? (currentPrice - entryPrice) / entryPrice
        : (entryPrice - currentPrice) / entryPrice;

    // Hard stop loss
    if (pnlPercent <= -this.STOP_LOSS_PERCENT) {
      return true;
    }

    // Take profit at target
    if (pnlPercent >= this.TAKE_PROFIT_PERCENT) {
      return true;
    }

    // Aggressive trailing stop - activate after 5% profit
    if (pnlPercent > 0.05) {
      const trailingStopPercent = 0.03; // 3% trailing stop

      if (this.position.side === "LONG" && this.position.highestPrice) {
        const trailingStopPrice =
          this.position.highestPrice * (1 - trailingStopPercent);
        if (currentPrice <= trailingStopPrice) {
          return true;
        }
      }

      if (this.position.side === "SHORT" && this.position.lowestPrice) {
        const trailingStopPrice =
          this.position.lowestPrice * (1 + trailingStopPercent);
        if (currentPrice >= trailingStopPrice) {
          return true;
        }
      }
    }

    // RSI extreme reversal signals
    if (this.position.side === "LONG" && indicators.rsi > 85) {
      return true;
    }

    if (this.position.side === "SHORT" && indicators.rsi < 15) {
      return true;
    }

    return false;
  }

  private calculatePositionSize(): number {
    // ULTIMATE AGGRESSION for 8x target
    let riskPercent = this.RISK_PER_TRADE;

    // Extreme scaling for maximum compound growth
    if (this.balance > this.initialBalance * 7) {
      riskPercent = 0.3; // Still very aggressive when 7x ahead
    } else if (this.balance > this.initialBalance * 5) {
      riskPercent = 0.35; // Aggressive when 5x ahead
    } else if (this.balance > this.initialBalance * 3) {
      riskPercent = 0.4; // Very aggressive when 3x ahead
    } else if (this.balance > this.initialBalance * 2) {
      riskPercent = 0.45; // Ultra aggressive when 2x ahead
    } else {
      riskPercent = 0.5; // MAXIMUM aggression when behind
    }

    const currentRiskAmount = this.balance * riskPercent;
    const maxRiskAmount = this.balance * 0.7; // Max 70% of current balance

    const riskAmount = Math.min(currentRiskAmount, maxRiskAmount);
    return riskAmount * this.LEVERAGE;
  }

  private openPosition(
    side: "LONG" | "SHORT",
    price: number,
    time: string
  ): void {
    const size = this.calculatePositionSize();

    this.position = {
      side,
      size,
      entryPrice: price,
      entryTime: time,
      leverage: this.LEVERAGE,
      highestPrice: side === "LONG" ? price : undefined,
      lowestPrice: side === "SHORT" ? price : undefined,
    };

    console.log(
      `Opened ${side} position at ${price} with size ${size.toFixed(
        4
      )} (${time})`
    );
  }

  private closePosition(price: number, time: string): void {
    if (!this.position.side) return;

    const priceChangePercent =
      this.position.side === "LONG"
        ? (price - this.position.entryPrice) / this.position.entryPrice
        : (this.position.entryPrice - price) / this.position.entryPrice;

    const positionValue = this.position.size / this.LEVERAGE;
    const pnl = positionValue * priceChangePercent * this.LEVERAGE;
    const pnlPercent = priceChangePercent * this.LEVERAGE * 100;

    // Cap maximum loss to prevent account blowup
    const maxLoss = this.balance * 0.06; // Max 6% loss per trade
    const cappedPnl = Math.max(pnl, -maxLoss);

    this.balance += cappedPnl;

    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }

    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    const trade: Trade = {
      entryTime: this.position.entryTime,
      exitTime: time,
      side: this.position.side,
      entryPrice: this.position.entryPrice,
      exitPrice: price,
      size: this.position.size,
      pnl: cappedPnl,
      pnlPercent,
      leverage: this.position.leverage,
    };

    this.trades.push(trade);

    console.log(
      `Closed ${
        this.position.side
      } position at ${price}. PnL: ${cappedPnl.toFixed(
        2
      )} USDT (${pnlPercent.toFixed(2)}%). Balance: ${this.balance.toFixed(
        2
      )} USDT`
    );

    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      highestPrice: undefined,
      lowestPrice: undefined,
    };
  }

  async runBacktest(): Promise<BacktestResult> {
    console.log("🚀 Starting ULTIMATE MOMENTUM HUNTER backtest...");
    console.log(`Initial balance: ${this.initialBalance} USDT`);
    console.log(`Target: ${this.initialBalance * 8} USDT (8x return)`);
    console.log(`Leverage: ${this.LEVERAGE}x`);
    console.log(`Risk per trade: ${this.RISK_PER_TRADE * 100}%`);

    for (let i = 1; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      if (!indicators) continue;

      // Check for exit signals if position exists
      if (this.position.side && this.shouldExitPosition(candle, indicators)) {
        this.closePosition(candle.close, candle.time);
      }

      // Check for entry signals if no position
      if (!this.position.side) {
        if (this.shouldEnterLong(candle, indicators, prevCandle)) {
          this.openPosition("LONG", candle.close, candle.time);
        } else if (this.shouldEnterShort(candle, indicators, prevCandle)) {
          this.openPosition("SHORT", candle.close, candle.time);
        }
      }

      // Emergency exit if balance drops too low
      if (this.balance < this.initialBalance * 0.1) {
        console.log("⚠️ Emergency exit: Balance dropped below 10% of initial");
        if (this.position.side) {
          this.closePosition(candle.close, candle.time);
        }
        break;
      }
    }

    // Close any remaining position
    if (this.position.side) {
      const lastCandle = this.data[this.data.length - 1];
      this.closePosition(lastCandle.close, lastCandle.time);
    }

    const winningTrades = this.trades.filter((t) => t.pnl > 0).length;
    const losingTrades = this.trades.filter((t) => t.pnl <= 0).length;
    const totalReturn = this.balance - this.initialBalance;
    const totalReturnPercent = (totalReturn / this.initialBalance) * 100;

    // Calculate Sharpe ratio (simplified)
    const returns = this.trades.map((t) => t.pnlPercent / 100);
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    const returnStdDev = Math.sqrt(
      returns.reduce((a, b) => a + Math.pow(b - avgReturn, 2), 0) /
        returns.length
    );
    const sharpeRatio = returnStdDev > 0 ? avgReturn / returnStdDev : 0;

    const result: BacktestResult = {
      initialBalance: this.initialBalance,
      finalBalance: this.balance,
      totalReturn,
      totalReturnPercent,
      totalTrades: this.trades.length,
      winningTrades,
      losingTrades,
      winRate:
        this.trades.length > 0 ? (winningTrades / this.trades.length) * 100 : 0,
      maxDrawdown: this.maxDrawdown * 100,
      sharpeRatio,
      trades: this.trades,
    };

    console.log("\n🎯 ULTIMATE MOMENTUM HUNTER Results:");
    console.log(`Final Balance: ${result.finalBalance.toFixed(2)} USDT`);
    console.log(
      `Total Return: ${result.totalReturn.toFixed(
        2
      )} USDT (${result.totalReturnPercent.toFixed(2)}%)`
    );
    console.log(`Total Trades: ${result.totalTrades}`);
    console.log(`Win Rate: ${result.winRate.toFixed(2)}%`);
    console.log(`Max Drawdown: ${result.maxDrawdown.toFixed(2)}%`);
    console.log(`Sharpe Ratio: ${result.sharpeRatio.toFixed(2)}`);

    if (result.finalBalance >= this.initialBalance * 8) {
      console.log("🎉 TARGET ACHIEVED! 8x return reached!");
    } else {
      console.log(
        `📊 Progress: ${(result.finalBalance / this.initialBalance).toFixed(
          2
        )}x of 8x target`
      );
    }

    return result;
  }
}

// Main execution
async function main() {
  const backtester = new OptimizedFuturesBacktester(100);

  try {
    await backtester.loadData("./data/historical_data_mar_15m.csv");
    const result = await backtester.runBacktest();

    // Save results to file
    fs.writeFileSync(
      "./optimized_backtest_results.json",
      JSON.stringify(result, null, 2)
    );
    console.log("\n📁 Results saved to optimized_backtest_results.json");
  } catch (error) {
    console.error("Error running backtest:", error);
  }
}

// Run the main function
main();
