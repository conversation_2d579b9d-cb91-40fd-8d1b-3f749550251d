import * as csv from "csv-parser";
import * as fs from "fs";
import { EMA, RSI, SMA } from "technicalindicators";

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  entryReason: string;
}

interface Trade {
  side: "LONG" | "SHORT";
  entryPrice: number;
  exitPrice: number;
  entryTime: string;
  exitTime: string;
  size: number;
  pnl: number;
  pnlPercent: number;
  reason: string;
  entryReason: string;
  leverage: number;
  balanceBefore: number;
  balanceAfter: number;
  cycle: number;
}

interface TradingCycle {
  cycleNumber: number;
  startDate: string;
  endDate: string;
  startBalance: number;
  endBalance: number;
  profit: number;
  profitPercent: number;
  trades: Trade[];
  withdrawn: number;
  keptBalance: number;
}

export class Conservative20xStrategy {
  private data: CandleData[] = [];
  private balance: number;
  private initialBalance: number = 40;
  private position: Position;
  private trades: Trade[] = [];
  private cycles: TradingCycle[] = [];
  private currentCycle: number = 1;
  private cycleStartTime: string = "";
  private cycleStartBalance: number = 40;
  private totalWithdrawn: number = 0;
  private maxBalance: number;
  private maxDrawdown: number = 0;

  // CONSERVATIVE 20X STRATEGY PARAMETERS
  private readonly RSI_PERIOD = 14;
  private readonly EMA_FAST = 12;
  private readonly EMA_SLOW = 26;
  private readonly SMA_PERIOD = 50;

  // Conservative parameters
  private RSI_OVERSOLD = 25;
  private RSI_OVERBOUGHT = 75;
  private LEVERAGE = 25; // Much more conservative
  private POSITION_SIZE_PERCENT = 0.5; // 50% of current balance
  private STOP_LOSS_PERCENT = 0.015; // 1.5% stop loss
  private TAKE_PROFIT_PERCENT = 0.045; // 4.5% take profit (3:1 R:R)
  private VOLUME_MULTIPLIER = 1.8;
  private MOMENTUM_THRESHOLD = 0.008;

  // Cycle management
  private readonly CYCLE_DURATION_DAYS = 30; // 1 month cycles
  private readonly WITHDRAWAL_FREQUENCY_DAYS = 14; // Check every 2 weeks
  private readonly KEEP_BALANCE = 40; // Always keep 40 USDT

  constructor(initialBalance: number = 40) {
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.maxBalance = initialBalance;
    this.cycleStartBalance = initialBalance;

    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      entryReason: "",
    };

    console.log("🏦 CONSERVATIVE 20X STRATEGY WITH PROFIT WITHDRAWAL");
    console.log(`💰 Initial Balance: ${initialBalance} USDT`);
    console.log(`📊 Position Size: 50% of current balance`);
    console.log(`⏰ Cycle Duration: ${this.CYCLE_DURATION_DAYS} days`);
    console.log(
      `💸 Withdrawal Check: Every ${this.WITHDRAWAL_FREQUENCY_DAYS} days`
    );
    console.log(`🔒 Keep Balance: ${this.KEEP_BALANCE} USDT`);
  }

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          if (results.length > 0) {
            this.cycleStartTime = results[0].time;
          }
          resolve();
        })
        .on("error", reject);
    });
  }

  private shouldWithdrawProfits(currentTime: string): boolean {
    const currentDate = new Date(currentTime);
    const cycleStartDate = new Date(this.cycleStartTime);
    const daysDiff =
      (currentDate.getTime() - cycleStartDate.getTime()) /
      (1000 * 60 * 60 * 24);

    return (
      daysDiff >= this.WITHDRAWAL_FREQUENCY_DAYS &&
      this.balance > this.KEEP_BALANCE
    );
  }

  private withdrawProfits(currentTime: string): number {
    if (this.balance <= this.KEEP_BALANCE) return 0;

    const withdrawAmount = this.balance - this.KEEP_BALANCE;
    this.totalWithdrawn += withdrawAmount;
    this.balance = this.KEEP_BALANCE;

    console.log(`💸 PROFIT WITHDRAWAL at ${currentTime}`);
    console.log(`   Withdrawn: ${withdrawAmount.toFixed(2)} USDT`);
    console.log(`   Total Withdrawn: ${this.totalWithdrawn.toFixed(2)} USDT`);
    console.log(`   Remaining Balance: ${this.balance.toFixed(2)} USDT`);

    // Reset cycle start time for next withdrawal period
    this.cycleStartTime = currentTime;

    return withdrawAmount;
  }

  private shouldStartNewCycle(currentTime: string): boolean {
    const currentDate = new Date(currentTime);
    const cycleStartDate = new Date(this.cycleStartTime);
    const daysDiff =
      (currentDate.getTime() - cycleStartDate.getTime()) /
      (1000 * 60 * 60 * 24);

    return daysDiff >= this.CYCLE_DURATION_DAYS;
  }

  private startNewCycle(currentTime: string): void {
    // Complete current cycle
    const cycleEndBalance = this.balance;
    const cycleProfit = cycleEndBalance - this.cycleStartBalance;
    const cycleProfitPercent = (cycleProfit / this.cycleStartBalance) * 100;

    const cycle: TradingCycle = {
      cycleNumber: this.currentCycle,
      startDate: this.cycleStartTime,
      endDate: currentTime,
      startBalance: this.cycleStartBalance,
      endBalance: cycleEndBalance,
      profit: cycleProfit,
      profitPercent: cycleProfitPercent,
      trades: this.trades.filter((t) => t.cycle === this.currentCycle),
      withdrawn: 0, // Will be calculated separately
      keptBalance: this.KEEP_BALANCE,
    };

    this.cycles.push(cycle);

    console.log(`\n🔄 CYCLE ${this.currentCycle} COMPLETED`);
    console.log(`   Duration: ${cycle.startDate} → ${cycle.endDate}`);
    console.log(`   Start: ${cycle.startBalance.toFixed(2)} USDT`);
    console.log(`   End: ${cycle.endBalance.toFixed(2)} USDT`);
    console.log(
      `   Profit: ${cycle.profit.toFixed(
        2
      )} USDT (${cycle.profitPercent.toFixed(2)}%)`
    );
    console.log(`   Trades: ${cycle.trades.length}`);

    // Start new cycle
    this.currentCycle++;
    this.cycleStartTime = currentTime;
    this.cycleStartBalance = this.balance;
  }

  private calculatePositionSize(): number {
    return this.balance * this.POSITION_SIZE_PERCENT;
  }

  private calculateIndicators(index: number): any {
    const lookback = Math.min(100, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);

    const closes = candles.map((c) => c.close);

    const rsiValues = RSI.calculate({
      period: this.RSI_PERIOD,
      values: closes,
    });
    const emaFastValues = EMA.calculate({
      period: this.EMA_FAST,
      values: closes,
    });
    const emaSlowValues = EMA.calculate({
      period: this.EMA_SLOW,
      values: closes,
    });
    const smaValues = SMA.calculate({
      period: this.SMA_PERIOD,
      values: closes,
    });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast:
        emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow:
        emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      sma: smaValues[smaValues.length - 1] || closes[closes.length - 1],
    };
  }

  private shouldEnterLong(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData,
    index: number
  ): { enter: boolean; reason: string } {
    // Conservative long entry conditions
    const oversold = indicators.rsi < this.RSI_OVERSOLD;
    const emaGoldenCross = indicators.emaFast > indicators.emaSlow * 1.002;
    const aboveSMA = candle.close > indicators.sma;
    const greenCandle = candle.close > candle.open;
    const goodVolume =
      candle.volume >
      (this.data
        .slice(Math.max(0, index - 10), index)
        .reduce((sum, c) => sum + c.volume, 0) /
        10) *
        this.VOLUME_MULTIPLIER;
    const momentum =
      Math.abs((candle.close - prevCandle.close) / prevCandle.close) >
      this.MOMENTUM_THRESHOLD;

    if (oversold && emaGoldenCross && aboveSMA && greenCandle && goodVolume) {
      return { enter: true, reason: "OVERSOLD_GOLDEN_CROSS_MOMENTUM" };
    }

    if (emaGoldenCross && momentum && goodVolume && greenCandle) {
      return { enter: true, reason: "EMA_CROSS_MOMENTUM" };
    }

    return { enter: false, reason: "NO_SIGNAL" };
  }

  private shouldEnterShort(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData,
    index: number
  ): { enter: boolean; reason: string } {
    // Conservative short entry conditions
    const overbought = indicators.rsi > this.RSI_OVERBOUGHT;
    const emaDeathCross = indicators.emaFast < indicators.emaSlow * 0.998;
    const belowSMA = candle.close < indicators.sma;
    const redCandle = candle.close < candle.open;
    const goodVolume =
      candle.volume >
      (this.data
        .slice(Math.max(0, index - 10), index)
        .reduce((sum, c) => sum + c.volume, 0) /
        10) *
        this.VOLUME_MULTIPLIER;
    const momentum =
      Math.abs((candle.close - prevCandle.close) / prevCandle.close) >
      this.MOMENTUM_THRESHOLD;

    if (overbought && emaDeathCross && belowSMA && redCandle && goodVolume) {
      return { enter: true, reason: "OVERBOUGHT_DEATH_CROSS_MOMENTUM" };
    }

    if (emaDeathCross && momentum && goodVolume && redCandle) {
      return { enter: true, reason: "EMA_DEATH_MOMENTUM" };
    }

    return { enter: false, reason: "NO_SIGNAL" };
  }

  private shouldExit(
    candle: CandleData,
    indicators: any
  ): { exit: boolean; reason: string } {
    if (!this.position.side) return { exit: false, reason: "NO_POSITION" };

    const currentPrice = candle.close;
    const entryPrice = this.position.entryPrice;

    if (this.position.side === "LONG") {
      const stopLoss = entryPrice * (1 - this.STOP_LOSS_PERCENT);
      const takeProfit = entryPrice * (1 + this.TAKE_PROFIT_PERCENT);

      if (currentPrice <= stopLoss) {
        return { exit: true, reason: "STOP_LOSS" };
      }
      if (currentPrice >= takeProfit) {
        return { exit: true, reason: "TAKE_PROFIT" };
      }
      if (indicators.rsi > this.RSI_OVERBOUGHT) {
        return { exit: true, reason: "RSI_OVERBOUGHT" };
      }
    } else {
      const stopLoss = entryPrice * (1 + this.STOP_LOSS_PERCENT);
      const takeProfit = entryPrice * (1 - this.TAKE_PROFIT_PERCENT);

      if (currentPrice >= stopLoss) {
        return { exit: true, reason: "STOP_LOSS" };
      }
      if (currentPrice <= takeProfit) {
        return { exit: true, reason: "TAKE_PROFIT" };
      }
      if (indicators.rsi < this.RSI_OVERSOLD) {
        return { exit: true, reason: "RSI_OVERSOLD" };
      }
    }

    return { exit: false, reason: "HOLD" };
  }

  private openPosition(
    side: "LONG" | "SHORT",
    candle: CandleData,
    reason: string
  ): void {
    const positionSize = this.calculatePositionSize();
    const leverage = this.LEVERAGE;
    const contractSize = (positionSize * leverage) / candle.close;

    this.position = {
      side,
      size: contractSize,
      entryPrice: candle.close,
      entryTime: candle.time,
      leverage,
      entryReason: reason,
    };

    console.log(
      `🟢 OPEN ${side} at ${candle.close} | Size: ${positionSize.toFixed(
        2
      )} USDT (${leverage}x) | Balance: ${this.balance.toFixed(
        2
      )} | Reason: ${reason}`
    );
  }

  private closePosition(candle: CandleData, reason: string): void {
    if (!this.position.side) return;

    const exitPrice = candle.close;
    const entryPrice = this.position.entryPrice;
    const size = this.position.size;
    const leverage = this.position.leverage;

    let pnl = 0;
    if (this.position.side === "LONG") {
      pnl = size * (exitPrice - entryPrice);
    } else {
      pnl = size * (entryPrice - exitPrice);
    }

    const pnlPercent =
      (pnl / (this.balance * this.POSITION_SIZE_PERCENT)) * 100;
    const balanceBefore = this.balance;
    this.balance += pnl;

    // Update max balance and drawdown
    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }
    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    const trade: Trade = {
      side: this.position.side,
      entryPrice,
      exitPrice,
      entryTime: this.position.entryTime,
      exitTime: candle.time,
      size,
      pnl,
      pnlPercent,
      reason,
      entryReason: this.position.entryReason,
      leverage,
      balanceBefore,
      balanceAfter: this.balance,
      cycle: this.currentCycle,
    };

    this.trades.push(trade);

    const pnlColor = pnl > 0 ? "🟢" : "🔴";
    console.log(
      `${pnlColor} CLOSE ${this.position.side} | PnL: ${pnl.toFixed(
        2
      )} (${pnlPercent.toFixed(2)}%) | Balance: ${this.balance.toFixed(
        2
      )} | ${reason}`
    );

    // Reset position
    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      entryReason: "",
    };
  }

  async runBacktest(): Promise<void> {
    console.log(`\n🚀 Starting Conservative 20X Strategy Backtest`);
    console.log(`📊 Data points: ${this.data.length}`);
    console.log(
      `⏰ Period: ${this.data[0]?.time} → ${
        this.data[this.data.length - 1]?.time
      }`
    );

    for (let i = 50; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      // Check for profit withdrawal
      if (this.shouldWithdrawProfits(candle.time)) {
        this.withdrawProfits(candle.time);
      }

      // Check for new cycle
      if (this.shouldStartNewCycle(candle.time)) {
        this.startNewCycle(candle.time);
      }

      // Exit logic
      if (this.position.side) {
        const exitSignal = this.shouldExit(candle, indicators);
        if (exitSignal.exit) {
          this.closePosition(candle, exitSignal.reason);
        }
      }

      // Entry logic (only if no position)
      if (!this.position.side && this.balance >= 10) {
        // Minimum balance check
        const longSignal = this.shouldEnterLong(
          candle,
          indicators,
          prevCandle,
          i
        );
        const shortSignal = this.shouldEnterShort(
          candle,
          indicators,
          prevCandle,
          i
        );

        if (longSignal.enter) {
          this.openPosition("LONG", candle, longSignal.reason);
        } else if (shortSignal.enter) {
          this.openPosition("SHORT", candle, shortSignal.reason);
        }
      }
    }

    // Close any remaining position
    if (this.position.side) {
      this.closePosition(this.data[this.data.length - 1], "END_OF_DATA");
    }

    // Complete final cycle
    if (this.data.length > 0) {
      this.startNewCycle(this.data[this.data.length - 1].time);
    }

    this.printResults();
  }

  private printResults(): void {
    const finalBalance = this.balance;
    const totalReturn =
      finalBalance - this.initialBalance + this.totalWithdrawn;
    const returnPercent = (totalReturn / this.initialBalance) * 100;
    const returnMultiplier =
      (this.initialBalance + totalReturn) / this.initialBalance;

    const winningTrades = this.trades.filter((t) => t.pnl > 0);
    const losingTrades = this.trades.filter((t) => t.pnl <= 0);
    const winRate = (winningTrades.length / this.trades.length) * 100;
    const avgWin =
      winningTrades.length > 0
        ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) /
          winningTrades.length
        : 0;
    const avgLoss =
      losingTrades.length > 0
        ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length
        : 0;
    const profitFactor = Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : 0;

    console.log("\n" + "=".repeat(80));
    console.log("🏦 CONSERVATIVE 20X STRATEGY - FINAL RESULTS");
    console.log("=".repeat(80));
    console.log(`💰 Initial Balance: ${this.initialBalance.toFixed(2)} USDT`);
    console.log(`💰 Final Balance: ${finalBalance.toFixed(2)} USDT`);
    console.log(`💸 Total Withdrawn: ${this.totalWithdrawn.toFixed(2)} USDT`);
    console.log(
      `💎 Total Value: ${(finalBalance + this.totalWithdrawn).toFixed(2)} USDT`
    );
    console.log(
      `📈 Total Return: ${totalReturn.toFixed(2)} USDT (${returnPercent.toFixed(
        2
      )}%)`
    );
    console.log(`🚀 Return Multiplier: ${returnMultiplier.toFixed(2)}x`);
    console.log(
      `🎯 Target Achieved (21x): ${returnMultiplier >= 21 ? "✅ YES" : "❌ NO"}`
    );
    console.log(`📊 Total Trades: ${this.trades.length}`);
    console.log(`🏆 Win Rate: ${winRate.toFixed(2)}%`);
    console.log(`📉 Max Drawdown: ${(this.maxDrawdown * 100).toFixed(2)}%`);
    console.log(`💎 Profit Factor: ${profitFactor.toFixed(2)}`);
    console.log(`🔄 Trading Cycles: ${this.cycles.length}`);

    console.log("\n📊 CYCLE BREAKDOWN:");
    this.cycles.forEach((cycle) => {
      console.log(
        `   Cycle ${cycle.cycleNumber}: ${cycle.profit.toFixed(
          2
        )} USDT (${cycle.profitPercent.toFixed(2)}%) | ${
          cycle.trades.length
        } trades`
      );
    });
  }

  getResults(): any {
    const finalBalance = this.balance;
    const totalReturn =
      finalBalance - this.initialBalance + this.totalWithdrawn;
    const returnPercent = (totalReturn / this.initialBalance) * 100;
    const returnMultiplier =
      (this.initialBalance + totalReturn) / this.initialBalance;

    const winningTrades = this.trades.filter((t) => t.pnl > 0);
    const losingTrades = this.trades.filter((t) => t.pnl <= 0);
    const winRate = (winningTrades.length / this.trades.length) * 100;
    const avgWin =
      winningTrades.length > 0
        ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) /
          winningTrades.length
        : 0;
    const avgLoss =
      losingTrades.length > 0
        ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length
        : 0;
    const profitFactor = Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : 0;

    return {
      strategy: "Conservative 20X Strategy with Profit Withdrawal",
      initialBalance: this.initialBalance,
      finalBalance,
      totalWithdrawn: this.totalWithdrawn,
      totalValue: finalBalance + this.totalWithdrawn,
      totalReturn,
      returnPercent,
      returnMultiplier,
      maxBalance: this.maxBalance,
      maxDrawdown: this.maxDrawdown * 100,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,
      avgWin,
      avgLoss,
      profitFactor,
      targetAchieved: returnMultiplier >= 21,
      trades: this.trades,
      cycles: this.cycles,
      dataStart: this.data[0]?.time,
      dataEnd: this.data[this.data.length - 1]?.time,
    };
  }
}

// Main execution function
async function main() {
  const strategy = new Conservative20xStrategy(40); // Start with 40 USDT

  try {
    // Load your specific dataset
    const dataFile =
      "./data/history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv";
    console.log(`🚀 Running Conservative 20X Strategy on: ${dataFile}`);

    await strategy.loadData(dataFile);
    await strategy.runBacktest();

    // Get results for saving
    const result = strategy.getResults();

    // Save results
    const resultsFile = `./results/backtests/conservative_20x_${Date.now()}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to: ${resultsFile}`);
  } catch (error) {
    console.error("❌ Error running backtest:", error);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
