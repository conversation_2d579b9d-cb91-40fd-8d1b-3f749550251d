import csv from "csv-parser";
import fs from "fs";
import { EMA, RSI, SMA } from "technicalindicators";

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  highestPrice?: number;
  lowestPrice?: number;
  entryReason: string;
}

interface Trade {
  side: "LONG" | "SHORT";
  entryPrice: number;
  exitPrice: number;
  entryTime: string;
  exitTime: string;
  size: number;
  pnl: number;
  pnlPercent: number;
  reason: string;
  entryReason: string;
  leverage: number;
  balanceBefore: number;
  balanceAfter: number;
}

interface MarketState {
  volatility: number;
  momentum: number;
  volume: number;
  trend: number;
  regime: "EXPLOSIVE" | "TRENDING" | "CHOPPY" | "DEAD";
}

export class Ultimate20xStrategy {
  private data: CandleData[] = [];
  private balance: number;
  private initialBalance: number;
  private position: Position;
  private trades: Trade[] = [];
  private maxBalance: number;
  private maxDrawdown: number = 0;
  private marketState: MarketState;

  // ULTIMATE 20X STRATEGY PARAMETERS - ULTRA AGGRESSIVE
  private readonly RSI_PERIOD = 14;
  private readonly EMA_FAST = 8;
  private readonly EMA_SLOW = 21;
  private readonly SMA_PERIOD = 50;
  private readonly VOLATILITY_PERIOD = 20;

  // Dynamic parameters for 20x target - MAXIMUM AGGRESSION
  private RSI_OVERSOLD = 5;
  private RSI_OVERBOUGHT = 95;
  private LEVERAGE = 100; // ULTRA HIGH LEVERAGE
  private RISK_PER_TRADE = 0.7; // 70% risk per trade for explosive growth
  private STOP_LOSS_PERCENT = 0.003; // 0.3% ultra tight stop
  private TAKE_PROFIT_PERCENT = 0.6; // 60% take profit (200:1 R:R)
  private VOLUME_MULTIPLIER = 1.5;
  private MOMENTUM_THRESHOLD = 0.015; // 1.5% momentum threshold

  constructor(initialBalance: number = 40) {
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.maxBalance = initialBalance;

    // Initialize position
    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      entryReason: "",
    };

    // Initialize market state
    this.marketState = {
      volatility: 0,
      momentum: 0,
      volume: 0,
      trend: 0,
      regime: "EXPLOSIVE",
    };

    console.log("🚀 ULTIMATE 20X STRATEGY INITIALIZED");
    console.log(
      `💰 Target: ${initialBalance} USDT → ${initialBalance *
        21} USDT (21x return)`
    );
    console.log("⚡ MAXIMUM AGGRESSION MODE ACTIVATED");
  }

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          resolve();
        })
        .on("error", reject);
    });
  }

  private calculateMarketState(index: number): MarketState {
    const lookback = Math.min(20, index);
    if (lookback < 10) {
      return {
        volatility: 0,
        momentum: 0,
        volume: 0,
        trend: 0,
        regime: "EXPLOSIVE",
      };
    }

    const recentCandles = this.data.slice(index - lookback, index + 1);
    const prices = recentCandles.map((c) => c.close);
    const volumes = recentCandles.map((c) => c.volume);

    // Calculate volatility (standard deviation of returns)
    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    const volatility = Math.sqrt(
      returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) /
        returns.length
    );

    // Calculate momentum (price change over period)
    const momentum = Math.abs(
      (prices[prices.length - 1] - prices[0]) / prices[0]
    );

    // Calculate volume ratio
    const avgVolume =
      volumes.slice(0, -5).reduce((a, b) => a + b, 0) / (volumes.length - 5);
    const recentVolume = volumes.slice(-5).reduce((a, b) => a + b, 0) / 5;
    const volumeRatio = recentVolume / avgVolume;

    // Calculate trend strength
    const smaValues = SMA.calculate({
      period: Math.min(10, lookback),
      values: prices,
    });
    const currentSMA = smaValues[smaValues.length - 1];
    const pastSMA = smaValues[Math.max(0, smaValues.length - 5)];
    const trend = Math.abs((currentSMA - pastSMA) / pastSMA);

    // Determine market regime for 20x strategy - MORE AGGRESSIVE THRESHOLDS
    let regime: "EXPLOSIVE" | "TRENDING" | "CHOPPY" | "DEAD";

    if (volatility > 0.025 && volumeRatio > 1.8 && momentum > 0.015) {
      regime = "EXPLOSIVE"; // Ultra explosive conditions
    } else if (volatility > 0.012 && volumeRatio > 1.2 && trend > 0.004) {
      regime = "TRENDING"; // Strong trending
    } else if (volatility > 0.006 && volumeRatio > 0.7) {
      regime = "CHOPPY"; // Choppy but tradeable
    } else {
      regime = "DEAD"; // Dead market
    }

    return {
      volatility,
      momentum,
      volume: volumeRatio,
      trend,
      regime,
    };
  }

  private adaptToMarketState(state: MarketState): void {
    // 20X STRATEGY - ULTRA AGGRESSIVE ADAPTATION
    switch (state.regime) {
      case "EXPLOSIVE":
        // MAXIMUM AGGRESSION for explosive moves
        this.RSI_OVERSOLD = 3;
        this.RSI_OVERBOUGHT = 97;
        this.LEVERAGE = 150; // MAXIMUM LEVERAGE
        this.RISK_PER_TRADE = 0.8; // 80% risk
        this.STOP_LOSS_PERCENT = 0.002; // 0.2% stop
        this.TAKE_PROFIT_PERCENT = 0.8; // 80% target (400:1 R:R)
        this.VOLUME_MULTIPLIER = 1.2;
        this.MOMENTUM_THRESHOLD = 0.02;
        break;

      case "TRENDING":
        // High aggression for trending
        this.RSI_OVERSOLD = 5;
        this.RSI_OVERBOUGHT = 95;
        this.LEVERAGE = 120;
        this.RISK_PER_TRADE = 0.7;
        this.STOP_LOSS_PERCENT = 0.003;
        this.TAKE_PROFIT_PERCENT = 0.6;
        this.VOLUME_MULTIPLIER = 1.5;
        this.MOMENTUM_THRESHOLD = 0.015;
        break;

      case "CHOPPY":
        // Moderate aggression for choppy markets
        this.RSI_OVERSOLD = 10;
        this.RSI_OVERBOUGHT = 90;
        this.LEVERAGE = 80;
        this.RISK_PER_TRADE = 0.5;
        this.STOP_LOSS_PERCENT = 0.005;
        this.TAKE_PROFIT_PERCENT = 0.4;
        this.VOLUME_MULTIPLIER = 2.0;
        this.MOMENTUM_THRESHOLD = 0.01;
        break;

      case "DEAD":
        // Conservative but still aggressive for dead markets
        this.RSI_OVERSOLD = 20;
        this.RSI_OVERBOUGHT = 80;
        this.LEVERAGE = 50;
        this.RISK_PER_TRADE = 0.3;
        this.STOP_LOSS_PERCENT = 0.01;
        this.TAKE_PROFIT_PERCENT = 0.2;
        this.VOLUME_MULTIPLIER = 3.0;
        this.MOMENTUM_THRESHOLD = 0.005;
        break;
    }
  }

  private calculateIndicators(index: number): any {
    const lookback = Math.min(100, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);

    const closes = candles.map((c) => c.close);
    const highs = candles.map((c) => c.high);
    const lows = candles.map((c) => c.low);

    // Calculate indicators
    const rsiValues = RSI.calculate({
      period: this.RSI_PERIOD,
      values: closes,
    });
    const emaFastValues = EMA.calculate({
      period: this.EMA_FAST,
      values: closes,
    });
    const emaSlowValues = EMA.calculate({
      period: this.EMA_SLOW,
      values: closes,
    });
    const smaValues = SMA.calculate({
      period: this.SMA_PERIOD,
      values: closes,
    });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 0,
      emaFast: emaFastValues[emaFastValues.length - 1] || 0,
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || 0,
      sma: smaValues[smaValues.length - 1] || 0,
    };
  }

  private shouldEnterLong(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData,
    state: MarketState,
    index: number
  ): { enter: boolean; reason: string } {
    // ULTIMATE 20X LONG ENTRY LOGIC - ULTRA AGGRESSIVE

    // Core conditions
    const extremeOversold = indicators.rsi < this.RSI_OVERSOLD;
    const rsiRecovering =
      indicators.rsi > this.RSI_OVERSOLD &&
      indicators.rsi < this.RSI_OVERSOLD + 15;

    // Price action signals
    const explosiveGreenCandle =
      (candle.close - candle.open) / candle.open > 0.008;
    const engulfingPattern =
      candle.close > prevCandle.high && candle.open < prevCandle.low;
    const gapUp = candle.open > prevCandle.close * 1.002;

    // Volume and momentum
    const ultraVolume =
      candle.volume >
      (this.data
        .slice(Math.max(0, index - 10), index)
        .reduce((sum, c) => sum + c.volume, 0) /
        10) *
        this.VOLUME_MULTIPLIER;
    const strongMomentum =
      Math.abs((candle.close - prevCandle.close) / prevCandle.close) >
      this.MOMENTUM_THRESHOLD;

    // EMA signals
    const emaGoldenCross = indicators.emaFast > indicators.emaSlow * 1.001;
    const emaGap =
      (indicators.emaFast - indicators.emaSlow) / indicators.emaSlow > 0.002;

    // SMA breakout
    const breakoutAboveSMA = candle.close > indicators.sma * 1.003;

    // Trend signals
    const strongBullishTrend =
      indicators.emaFast > indicators.emaSlow &&
      candle.close > indicators.emaFast;
    const consecutiveGreen = this.data
      .slice(Math.max(0, index - 2), index + 1)
      .every((c) => c.close > c.open);

    // 20X REGIME-SPECIFIC ULTRA AGGRESSIVE ENTRY LOGIC
    switch (state.regime) {
      case "EXPLOSIVE":
        // MAXIMUM AGGRESSION - Only the most explosive setups
        if (extremeOversold && explosiveGreenCandle && ultraVolume && gapUp) {
          return { enter: true, reason: "EXTREME_OVERSOLD_EXPLOSIVE_GAP" };
        }
        if (
          emaGoldenCross &&
          strongMomentum &&
          ultraVolume &&
          breakoutAboveSMA
        ) {
          return { enter: true, reason: "EMA_CROSS_MOMENTUM_BREAKOUT" };
        }
        if (
          engulfingPattern &&
          ultraVolume &&
          strongBullishTrend &&
          consecutiveGreen
        ) {
          return { enter: true, reason: "ENGULFING_TREND_CONTINUATION" };
        }
        break;

      case "TRENDING":
        // High aggression for trending markets
        if (
          breakoutAboveSMA &&
          strongMomentum &&
          ultraVolume &&
          indicators.rsi < 30
        ) {
          return { enter: true, reason: "SMA_BREAKOUT_TREND_20X" };
        }
        if (rsiRecovering && engulfingPattern && ultraVolume && emaGap) {
          return { enter: true, reason: "RSI_RECOVERY_ENGULFING_20X" };
        }
        if (strongBullishTrend && explosiveGreenCandle && ultraVolume) {
          return { enter: true, reason: "TREND_MOMENTUM_20X" };
        }
        break;

      case "CHOPPY":
        // Moderate aggression for choppy markets
        if (extremeOversold && explosiveGreenCandle && ultraVolume) {
          return { enter: true, reason: "OVERSOLD_BOUNCE_20X" };
        }
        if (emaGoldenCross && strongMomentum && ultraVolume) {
          return { enter: true, reason: "EMA_CROSS_CHOPPY_20X" };
        }
        break;

      case "DEAD":
        // Conservative but still aggressive
        if (
          extremeOversold &&
          explosiveGreenCandle &&
          ultraVolume &&
          breakoutAboveSMA
        ) {
          return { enter: true, reason: "DEAD_MARKET_BREAKOUT_20X" };
        }
        break;
    }

    return { enter: false, reason: "NO_SIGNAL" };
  }

  private shouldEnterShort(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData,
    state: MarketState,
    index: number
  ): { enter: boolean; reason: string } {
    // ULTIMATE 20X SHORT ENTRY LOGIC - ULTRA AGGRESSIVE

    // Core conditions
    const extremeOverbought = indicators.rsi > this.RSI_OVERBOUGHT;
    const rsiWeakening =
      indicators.rsi < this.RSI_OVERBOUGHT &&
      indicators.rsi > this.RSI_OVERBOUGHT - 15;

    // Price action signals
    const explosiveRedCandle =
      (candle.open - candle.close) / candle.open > 0.008;
    const bearishEngulfing =
      candle.close < prevCandle.low && candle.open > prevCandle.high;
    const gapDown = candle.open < prevCandle.close * 0.998;

    // Volume and momentum
    const ultraVolume =
      candle.volume >
      (this.data
        .slice(Math.max(0, index - 10), index)
        .reduce((sum, c) => sum + c.volume, 0) /
        10) *
        this.VOLUME_MULTIPLIER;
    const strongMomentum =
      Math.abs((candle.close - prevCandle.close) / prevCandle.close) >
      this.MOMENTUM_THRESHOLD;

    // EMA signals
    const emaDeathCross = indicators.emaFast < indicators.emaSlow * 0.999;
    const emaGap =
      (indicators.emaSlow - indicators.emaFast) / indicators.emaFast > 0.002;

    // SMA breakdown
    const breakdownBelowSMA = candle.close < indicators.sma * 0.997;

    // Trend signals
    const strongBearishTrend =
      indicators.emaFast < indicators.emaSlow &&
      candle.close < indicators.emaFast;
    const consecutiveRed = this.data
      .slice(Math.max(0, index - 2), index + 1)
      .every((c) => c.close < c.open);

    // 20X REGIME-SPECIFIC ULTRA AGGRESSIVE SHORT ENTRY LOGIC
    switch (state.regime) {
      case "EXPLOSIVE":
        // MAXIMUM AGGRESSION - Only the most explosive setups
        if (extremeOverbought && explosiveRedCandle && ultraVolume && gapDown) {
          return { enter: true, reason: "EXTREME_OVERBOUGHT_EXPLOSIVE_GAP" };
        }
        if (
          emaDeathCross &&
          strongMomentum &&
          ultraVolume &&
          breakdownBelowSMA
        ) {
          return { enter: true, reason: "EMA_DEATH_MOMENTUM_BREAKDOWN" };
        }
        if (
          bearishEngulfing &&
          ultraVolume &&
          strongBearishTrend &&
          consecutiveRed
        ) {
          return { enter: true, reason: "ENGULFING_TREND_BREAKDOWN" };
        }
        break;

      case "TRENDING":
        // High aggression for trending markets
        if (
          breakdownBelowSMA &&
          strongMomentum &&
          ultraVolume &&
          indicators.rsi > 70
        ) {
          return { enter: true, reason: "SMA_BREAKDOWN_TREND_20X" };
        }
        if (rsiWeakening && bearishEngulfing && ultraVolume && emaGap) {
          return { enter: true, reason: "RSI_WEAKENING_ENGULFING_20X" };
        }
        if (strongBearishTrend && explosiveRedCandle && ultraVolume) {
          return { enter: true, reason: "TREND_MOMENTUM_SHORT_20X" };
        }
        break;

      case "CHOPPY":
        // Moderate aggression for choppy markets
        if (extremeOverbought && explosiveRedCandle && ultraVolume) {
          return { enter: true, reason: "OVERBOUGHT_DUMP_20X" };
        }
        if (emaDeathCross && strongMomentum && ultraVolume) {
          return { enter: true, reason: "EMA_DEATH_CHOPPY_20X" };
        }
        break;

      case "DEAD":
        // Conservative but still aggressive
        if (
          extremeOverbought &&
          explosiveRedCandle &&
          ultraVolume &&
          breakdownBelowSMA
        ) {
          return { enter: true, reason: "DEAD_MARKET_BREAKDOWN_20X" };
        }
        break;
    }

    return { enter: false, reason: "NO_SIGNAL" };
  }

  private shouldExitPosition(
    candle: CandleData,
    indicators: any,
    state: MarketState
  ): { exit: boolean; reason: string } {
    if (!this.position.side) return { exit: false, reason: "NO_POSITION" };

    const currentPrice = candle.close;
    const entryPrice = this.position.entryPrice;
    const pnlPercent =
      this.position.side === "LONG"
        ? (currentPrice - entryPrice) / entryPrice
        : (entryPrice - currentPrice) / entryPrice;

    // Update highest/lowest prices for trailing stops
    if (this.position.side === "LONG") {
      if (
        !this.position.highestPrice ||
        currentPrice > this.position.highestPrice
      ) {
        this.position.highestPrice = currentPrice;
      }
    } else if (this.position.side === "SHORT") {
      if (
        !this.position.lowestPrice ||
        currentPrice < this.position.lowestPrice
      ) {
        this.position.lowestPrice = currentPrice;
      }
    }

    // HARD STOP LOSS - NEVER exceed this (ultra tight for 20x strategy)
    if (pnlPercent <= -this.STOP_LOSS_PERCENT) {
      return { exit: true, reason: "STOP_LOSS" };
    }

    // TAKE PROFIT at target (ultra high for 20x strategy)
    if (pnlPercent >= this.TAKE_PROFIT_PERCENT) {
      return { exit: true, reason: "TAKE_PROFIT" };
    }

    // 20X REGIME-SPECIFIC TRAILING STOPS AND EXITS
    let trailingActivation = 0.03; // 3% activation
    let trailingStopPercent = 0.01; // 1% trailing
    let rsiExitLong = 90;
    let rsiExitShort = 10;

    switch (state.regime) {
      case "EXPLOSIVE":
        trailingActivation = 0.05; // 5% activation for explosive moves
        trailingStopPercent = 0.008; // 0.8% trailing
        rsiExitLong = 97;
        rsiExitShort = 3;
        break;
      case "TRENDING":
        trailingActivation = 0.04;
        trailingStopPercent = 0.01;
        rsiExitLong = 95;
        rsiExitShort = 5;
        break;
      case "CHOPPY":
        trailingActivation = 0.02;
        trailingStopPercent = 0.012;
        rsiExitLong = 85;
        rsiExitShort = 15;
        break;
      case "DEAD":
        trailingActivation = 0.015;
        trailingStopPercent = 0.015;
        rsiExitLong = 75;
        rsiExitShort = 25;
        break;
    }

    // Trailing stop logic
    if (pnlPercent > trailingActivation) {
      if (this.position.side === "LONG" && this.position.highestPrice) {
        const trailingStopPrice =
          this.position.highestPrice * (1 - trailingStopPercent);
        if (currentPrice <= trailingStopPrice) {
          return { exit: true, reason: "TRAILING_STOP" };
        }
      }

      if (this.position.side === "SHORT" && this.position.lowestPrice) {
        const trailingStopPrice =
          this.position.lowestPrice * (1 + trailingStopPercent);
        if (currentPrice >= trailingStopPrice) {
          return { exit: true, reason: "TRAILING_STOP" };
        }
      }
    }

    // RSI reversal signals
    if (this.position.side === "LONG" && indicators.rsi > rsiExitLong) {
      return { exit: true, reason: "RSI_OVERBOUGHT_EXIT" };
    }

    if (this.position.side === "SHORT" && indicators.rsi < rsiExitShort) {
      return { exit: true, reason: "RSI_OVERSOLD_EXIT" };
    }

    // Emergency exits for regime changes
    if (state.regime === "DEAD" && pnlPercent > 0.005) {
      // Take very quick profits in dead markets
      return { exit: true, reason: "DEAD_MARKET_QUICK_PROFIT" };
    }

    return { exit: false, reason: "HOLD" };
  }

  private calculatePositionSize(state: MarketState): number {
    // ULTRA-AGGRESSIVE POSITION SIZING FOR 20X TARGET
    let riskPercent = this.RISK_PER_TRADE;
    let leverageMultiplier = 1.0;

    // Account growth scaling - MAXIMUM AGGRESSION for compound growth
    const balanceMultiplier = this.balance / this.initialBalance;

    if (balanceMultiplier >= 15) {
      // When 15x ahead, reduce risk slightly but stay aggressive
      riskPercent *= 0.9;
      leverageMultiplier *= 0.95;
    } else if (balanceMultiplier >= 10) {
      // When 10x ahead, maintain high aggression
      riskPercent *= 1.0;
      leverageMultiplier *= 1.0;
    } else if (balanceMultiplier >= 5) {
      // When 5x ahead, increase aggression
      riskPercent *= 1.1;
      leverageMultiplier *= 1.05;
    } else if (balanceMultiplier >= 2) {
      // When 2x ahead, maximum aggression
      riskPercent *= 1.2;
      leverageMultiplier *= 1.1;
    } else {
      // When behind or starting, ULTRA maximum aggression
      riskPercent *= 1.3;
      leverageMultiplier *= 1.15;
    }

    // Market regime scaling for 20x strategy
    switch (state.regime) {
      case "EXPLOSIVE":
        riskPercent *= 1.2; // 20% more aggressive
        leverageMultiplier *= 1.1;
        break;
      case "TRENDING":
        riskPercent *= 1.1; // 10% more aggressive
        leverageMultiplier *= 1.05;
        break;
      case "CHOPPY":
        riskPercent *= 1.0; // Normal aggression
        leverageMultiplier *= 1.0;
        break;
      case "DEAD":
        riskPercent *= 0.9; // 10% less aggressive
        leverageMultiplier *= 0.95;
        break;
    }

    // Cap maximum risk and leverage for 20x strategy
    const finalRiskPercent = Math.min(riskPercent, 0.85); // Max 85% risk
    const finalLeverage = Math.min(this.LEVERAGE * leverageMultiplier, 200); // Max 200x leverage

    const positionSize = this.balance * finalRiskPercent;
    const leveragedSize = positionSize * finalLeverage;

    return leveragedSize;
  }

  private openPosition(
    side: "LONG" | "SHORT",
    price: number,
    time: string,
    reason: string,
    state: MarketState
  ): void {
    const size = this.calculatePositionSize(state);

    this.position = {
      side,
      size,
      entryPrice: price,
      entryTime: time,
      leverage: this.LEVERAGE,
      highestPrice: side === "LONG" ? price : undefined,
      lowestPrice: side === "SHORT" ? price : undefined,
      entryReason: reason,
    };

    console.log(
      `[${state.regime}] 🚀 20X ${side} at ${price} | Size: ${size.toFixed(
        4
      )} | Reason: ${reason} | Balance: ${this.balance.toFixed(2)} (${time})`
    );
  }

  private closePosition(price: number, time: string, reason: string): void {
    if (!this.position.side) return;

    const pnl =
      this.position.side === "LONG"
        ? ((price - this.position.entryPrice) * this.position.size) /
          this.position.entryPrice
        : ((this.position.entryPrice - price) * this.position.size) /
          this.position.entryPrice;

    const pnlPercent =
      this.position.side === "LONG"
        ? (price - this.position.entryPrice) / this.position.entryPrice
        : (this.position.entryPrice - price) / this.position.entryPrice;

    const balanceBefore = this.balance;
    this.balance += pnl;

    // Update max balance and drawdown
    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }
    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    const trade: Trade = {
      side: this.position.side,
      entryPrice: this.position.entryPrice,
      exitPrice: price,
      entryTime: this.position.entryTime,
      exitTime: time,
      size: this.position.size,
      pnl,
      pnlPercent,
      reason,
      entryReason: this.position.entryReason,
      leverage: this.position.leverage,
      balanceBefore,
      balanceAfter: this.balance,
    };

    this.trades.push(trade);

    const pnlColor = pnl > 0 ? "🟢" : "🔴";
    const balanceMultiplier = (this.balance / this.initialBalance).toFixed(2);

    console.log(
      `${pnlColor} 20X ${
        this.position.side
      } EXIT at ${price} | PnL: ${pnl.toFixed(2)} (${(pnlPercent * 100).toFixed(
        2
      )}%) | Balance: ${this.balance.toFixed(
        2
      )} (${balanceMultiplier}x) | Reason: ${reason} (${time})`
    );

    // Reset position
    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      entryReason: "",
    };
  }

  async runBacktest(): Promise<void> {
    if (this.data.length === 0) {
      throw new Error("No data loaded. Call loadData() first.");
    }

    console.log("\n🚀 STARTING ULTIMATE 20X STRATEGY BACKTEST");
    console.log("=".repeat(60));
    console.log(`📊 Data points: ${this.data.length}`);
    console.log(`💰 Initial balance: ${this.initialBalance} USDT`);
    console.log(
      `🎯 Target balance: ${this.initialBalance * 21} USDT (21x return)`
    );
    console.log(`⚡ MAXIMUM AGGRESSION MODE`);
    console.log("=".repeat(60));

    const startTime = Date.now();

    for (let i = this.SMA_PERIOD; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];

      // Calculate indicators
      const indicators = this.calculateIndicators(i);

      // Calculate market state
      const currentState = this.calculateMarketState(i);

      // Adapt strategy to market state
      this.adaptToMarketState(currentState);
      this.marketState = currentState;

      // Check for exit signals if position exists
      if (this.position.side) {
        const exitSignal = this.shouldExitPosition(
          candle,
          indicators,
          currentState
        );
        if (exitSignal.exit) {
          this.closePosition(candle.close, candle.time, exitSignal.reason);
        }
      }

      // Check for entry signals if no position
      if (!this.position.side) {
        const longSignal = this.shouldEnterLong(
          candle,
          indicators,
          prevCandle,
          currentState,
          i
        );
        const shortSignal = this.shouldEnterShort(
          candle,
          indicators,
          prevCandle,
          currentState,
          i
        );

        if (longSignal.enter) {
          this.openPosition(
            "LONG",
            candle.close,
            candle.time,
            longSignal.reason,
            currentState
          );
        } else if (shortSignal.enter) {
          this.openPosition(
            "SHORT",
            candle.close,
            candle.time,
            shortSignal.reason,
            currentState
          );
        }
      }

      // Emergency exit if balance drops too low
      if (this.balance < this.initialBalance * 0.02) {
        console.log("⚠️ EMERGENCY EXIT: Balance dropped below 2% of initial");
        if (this.position.side) {
          this.closePosition(candle.close, candle.time, "EMERGENCY_EXIT");
        }
        break;
      }

      // SUCCESS CHECK - 20X TARGET ACHIEVED
      if (this.balance >= this.initialBalance * 21) {
        console.log(
          `🎉 20X TARGET ACHIEVED! Balance: ${this.balance.toFixed(
            2
          )} USDT at ${candle.time}`
        );
        if (this.position.side) {
          this.closePosition(candle.close, candle.time, "TARGET_ACHIEVED");
        }
        break;
      }

      // Progress update every 100 candles
      if (i % 100 === 0) {
        const progress = ((i / this.data.length) * 100).toFixed(1);
        const currentMultiplier = (this.balance / this.initialBalance).toFixed(
          2
        );
        console.log(
          `📈 Progress: ${progress}% | Balance: ${this.balance.toFixed(
            2
          )} USDT (${currentMultiplier}x) | Regime: ${currentState.regime}`
        );
      }
    }

    // Close any remaining position
    if (this.position.side) {
      const lastCandle = this.data[this.data.length - 1];
      this.closePosition(lastCandle.close, lastCandle.time, "END_OF_DATA");
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    this.printResults(duration);
  }

  private printResults(duration: number): void {
    const finalBalance = this.balance;
    const totalReturn = finalBalance - this.initialBalance;
    const returnPercent = (totalReturn / this.initialBalance) * 100;
    const returnMultiplier = finalBalance / this.initialBalance;

    const winningTrades = this.trades.filter((t) => t.pnl > 0);
    const losingTrades = this.trades.filter((t) => t.pnl < 0);
    const winRate =
      this.trades.length > 0
        ? (winningTrades.length / this.trades.length) * 100
        : 0;

    const totalPnL = this.trades.reduce((sum, t) => sum + t.pnl, 0);
    const avgWin =
      winningTrades.length > 0
        ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) /
          winningTrades.length
        : 0;
    const avgLoss =
      losingTrades.length > 0
        ? Math.abs(
            losingTrades.reduce((sum, t) => sum + t.pnl, 0) /
              losingTrades.length
          )
        : 0;
    const profitFactor = avgLoss > 0 ? avgWin / avgLoss : 0;

    console.log("\n" + "=".repeat(80));
    console.log("🚀 ULTIMATE 20X STRATEGY BACKTEST RESULTS");
    console.log("=".repeat(80));

    console.log("\n💰 PERFORMANCE SUMMARY:");
    console.log(
      `   Initial Balance:     ${this.initialBalance.toFixed(2)} USDT`
    );
    console.log(`   Final Balance:       ${finalBalance.toFixed(2)} USDT`);
    console.log(
      `   Total Return:        ${totalReturn.toFixed(
        2
      )} USDT (${returnPercent.toFixed(2)}%)`
    );
    console.log(`   Return Multiplier:   ${returnMultiplier.toFixed(2)}x`);
    console.log(`   Max Balance:         ${this.maxBalance.toFixed(2)} USDT`);
    console.log(
      `   Max Drawdown:        ${(this.maxDrawdown * 100).toFixed(2)}%`
    );

    const targetAchieved = returnMultiplier >= 21;
    const targetStatus = targetAchieved
      ? "🎉 TARGET ACHIEVED!"
      : "❌ Target not reached";
    console.log(`   20X Target Status:   ${targetStatus}`);

    console.log("\n📊 TRADING STATISTICS:");
    console.log(`   Total Trades:        ${this.trades.length}`);
    console.log(
      `   Winning Trades:      ${winningTrades.length} (${winRate.toFixed(1)}%)`
    );
    console.log(
      `   Losing Trades:       ${losingTrades.length} (${(
        100 - winRate
      ).toFixed(1)}%)`
    );
    console.log(`   Average Win:         ${avgWin.toFixed(2)} USDT`);
    console.log(`   Average Loss:        ${avgLoss.toFixed(2)} USDT`);
    console.log(`   Profit Factor:       ${profitFactor.toFixed(2)}`);
    console.log(`   Total PnL:           ${totalPnL.toFixed(2)} USDT`);

    console.log("\n⏱️ EXECUTION:");
    console.log(`   Backtest Duration:   ${duration.toFixed(2)} seconds`);
    console.log(
      `   Data Period:         ${this.data[0].time} to ${
        this.data[this.data.length - 1].time
      }`
    );

    if (this.trades.length > 0) {
      console.log("\n🏆 TOP PERFORMING TRADES:");
      const topTrades = [...this.trades]
        .sort((a, b) => b.pnl - a.pnl)
        .slice(0, 5);

      topTrades.forEach((trade, index) => {
        console.log(
          `   ${index + 1}. ${trade.side} | PnL: ${trade.pnl.toFixed(
            2
          )} USDT (${(trade.pnlPercent * 100).toFixed(2)}%) | ${
            trade.entryReason
          } → ${trade.reason}`
        );
      });

      console.log("\n📉 WORST PERFORMING TRADES:");
      const worstTrades = [...this.trades]
        .sort((a, b) => a.pnl - b.pnl)
        .slice(0, 3);

      worstTrades.forEach((trade, index) => {
        console.log(
          `   ${index + 1}. ${trade.side} | PnL: ${trade.pnl.toFixed(
            2
          )} USDT (${(trade.pnlPercent * 100).toFixed(2)}%) | ${
            trade.entryReason
          } → ${trade.reason}`
        );
      });
    }

    console.log("\n" + "=".repeat(80));

    if (targetAchieved) {
      console.log("🎉 CONGRATULATIONS! 20X TARGET ACHIEVED!");
      console.log(
        `💎 You turned ${this.initialBalance} USDT into ${finalBalance.toFixed(
          2
        )} USDT`
      );
      console.log("🚀 ULTIMATE 20X STRATEGY SUCCESSFUL!");
    } else {
      console.log("📈 Strategy Performance Analysis:");
      const progressPercent = ((returnMultiplier / 21) * 100).toFixed(1);
      console.log(`   Progress to 20X target: ${progressPercent}%`);
      console.log(
        "💡 Consider optimizing parameters or trying different market conditions"
      );
    }

    console.log("=".repeat(80));
  }

  getResults() {
    const finalBalance = this.balance;
    const totalReturn = finalBalance - this.initialBalance;
    const returnPercent = (totalReturn / this.initialBalance) * 100;
    const returnMultiplier = finalBalance / this.initialBalance;

    const winningTrades = this.trades.filter((t) => t.pnl > 0);
    const losingTrades = this.trades.filter((t) => t.pnl < 0);
    const winRate =
      this.trades.length > 0
        ? (winningTrades.length / this.trades.length) * 100
        : 0;

    const avgWin =
      winningTrades.length > 0
        ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) /
          winningTrades.length
        : 0;
    const avgLoss =
      losingTrades.length > 0
        ? Math.abs(
            losingTrades.reduce((sum, t) => sum + t.pnl, 0) /
              losingTrades.length
          )
        : 0;
    const profitFactor = avgLoss > 0 ? avgWin / avgLoss : 0;

    return {
      strategy: "Ultimate 20X Strategy",
      initialBalance: this.initialBalance,
      finalBalance,
      totalReturn,
      returnPercent,
      returnMultiplier,
      maxBalance: this.maxBalance,
      maxDrawdown: this.maxDrawdown * 100,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,
      avgWin,
      avgLoss,
      profitFactor,
      targetAchieved: returnMultiplier >= 21,
      trades: this.trades,
      dataStart: this.data[0]?.time,
      dataEnd: this.data[this.data.length - 1]?.time,
    };
  }
}
