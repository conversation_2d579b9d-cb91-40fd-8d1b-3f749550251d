#!/usr/bin/env node

import fs from "fs";
import csv from "csv-parser";

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface BigMove {
  startTime: string;
  endTime: string;
  startPrice: number;
  endPrice: number;
  gain: number;
  duration: number; // in candles
  direction: "UP" | "DOWN";
}

class DataDrivenStrategy {
  private data: CandleData[] = [];

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          resolve();
        })
        .on("error", reject);
    });
  }

  private findBigMoves(): BigMove[] {
    const bigMoves: BigMove[] = [];
    const minGain = 100; // Look for 100%+ moves
    
    // Check different timeframes
    const timeframes = [4, 16, 96, 288, 672]; // 1h, 4h, 1d, 3d, 1w in 15-min candles
    
    for (const duration of timeframes) {
      for (let i = 0; i < this.data.length - duration; i += Math.floor(duration / 4)) {
        const startCandle = this.data[i];
        
        // Find highest and lowest in this period
        let highest = startCandle.close;
        let lowest = startCandle.close;
        let highestIndex = i;
        let lowestIndex = i;
        
        for (let j = i; j < i + duration && j < this.data.length; j++) {
          if (this.data[j].high > highest) {
            highest = this.data[j].high;
            highestIndex = j;
          }
          if (this.data[j].low < lowest) {
            lowest = this.data[j].low;
            lowestIndex = j;
          }
        }
        
        // Calculate gains
        const upGain = (highest - startCandle.close) / startCandle.close * 100;
        const downGain = (startCandle.close - lowest) / startCandle.close * 100;
        
        if (upGain >= minGain) {
          bigMoves.push({
            startTime: startCandle.time,
            endTime: this.data[highestIndex].time,
            startPrice: startCandle.close,
            endPrice: highest,
            gain: upGain,
            duration,
            direction: "UP"
          });
        }
        
        if (downGain >= minGain) {
          bigMoves.push({
            startTime: startCandle.time,
            endTime: this.data[lowestIndex].time,
            startPrice: startCandle.close,
            endPrice: lowest,
            gain: downGain,
            duration,
            direction: "DOWN"
          });
        }
      }
    }
    
    // Remove duplicates and sort by gain
    return bigMoves
      .filter((move, index, self) => 
        index === self.findIndex(m => 
          Math.abs(new Date(m.startTime).getTime() - new Date(move.startTime).getTime()) < 3600000 && // Within 1 hour
          Math.abs(m.gain - move.gain) < 10 // Within 10% gain difference
        )
      )
      .sort((a, b) => b.gain - a.gain);
  }

  async analyzeDataset(filePath: string): Promise<any> {
    console.log(`\n📊 Analyzing ${filePath}...`);
    
    await this.loadData(filePath);
    const bigMoves = this.findBigMoves();
    
    const maxMove = bigMoves.length > 0 ? bigMoves[0].gain : 0;
    const movesOver500 = bigMoves.filter(m => m.gain >= 500).length;
    const movesOver1000 = bigMoves.filter(m => m.gain >= 1000).length;
    const movesOver2000 = bigMoves.filter(m => m.gain >= 2000).length;
    
    console.log(`   🚀 Max move: ${maxMove.toFixed(2)}%`);
    console.log(`   📈 500%+ moves: ${movesOver500}`);
    console.log(`   💎 1000%+ moves: ${movesOver1000}`);
    console.log(`   🎯 2000%+ moves: ${movesOver2000}`);
    
    return {
      file: filePath,
      maxMove,
      movesOver500,
      movesOver1000,
      movesOver2000,
      topMoves: bigMoves.slice(0, 5),
      totalCandles: this.data.length
    };
  }

  async analyzeAllDatasets(): Promise<void> {
    console.log("🔍 DATA-DRIVEN 20X STRATEGY ANALYSIS");
    console.log("Finding real opportunities for 2000%+ gains");
    console.log("=" .repeat(80));

    const dataDir = "./data";
    const files = fs.readdirSync(dataDir).filter(file => file.endsWith('.csv'));
    
    const results = [];
    
    for (const file of files) {
      const filePath = `${dataDir}/${file}`;
      const analysis = await this.analyzeDataset(filePath);
      results.push(analysis);
    }

    // Find overall statistics
    const maxMoveOverall = Math.max(...results.map(r => r.maxMove));
    const total2000Moves = results.reduce((sum, r) => sum + r.movesOver2000, 0);
    const total1000Moves = results.reduce((sum, r) => sum + r.movesOver1000, 0);
    const total500Moves = results.reduce((sum, r) => sum + r.movesOver500, 0);
    
    console.log("\n🎯 OVERALL ANALYSIS RESULTS");
    console.log("=" .repeat(60));
    console.log(`📊 Datasets analyzed: ${results.length}`);
    console.log(`🚀 Maximum move found: ${maxMoveOverall.toFixed(2)}%`);
    console.log(`🎯 Total 2000%+ moves: ${total2000Moves}`);
    console.log(`💎 Total 1000%+ moves: ${total1000Moves}`);
    console.log(`📈 Total 500%+ moves: ${total500Moves}`);
    
    // Find best dataset
    const bestDataset = results.reduce((best, current) => 
      current.maxMove > best.maxMove ? current : best
    );
    
    console.log(`\n🏆 BEST DATASET FOR 20X:`);
    console.log(`   File: ${bestDataset.file}`);
    console.log(`   Max move: ${bestDataset.maxMove.toFixed(2)}%`);
    console.log(`   2000%+ opportunities: ${bestDataset.movesOver2000}`);
    
    if (bestDataset.topMoves.length > 0) {
      console.log(`\n🚀 TOP OPPORTUNITIES:`);
      bestDataset.topMoves.forEach((move, index) => {
        const durationHours = (move.duration * 15 / 60).toFixed(1);
        console.log(`   ${index + 1}. ${move.gain.toFixed(2)}% in ${durationHours}h (${move.direction})`);
        console.log(`      ${move.startTime} → ${move.endTime}`);
        console.log(`      $${move.startPrice.toFixed(2)} → $${move.endPrice.toFixed(2)}`);
      });
    }
    
    // Generate strategy based on findings
    this.generateStrategy(maxMoveOverall, total2000Moves, bestDataset);
    
    // Save results
    fs.writeFileSync("data_driven_analysis.json", JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: {
        maxMoveOverall,
        total2000Moves,
        total1000Moves,
        total500Moves,
        bestDataset: bestDataset.file
      },
      results
    }, null, 2));
    
    console.log("\n💾 Analysis saved to data_driven_analysis.json");
  }

  private generateStrategy(maxMove: number, total2000Moves: number, bestDataset: any): void {
    console.log("\n💡 DATA-DRIVEN 20X STRATEGY");
    console.log("=" .repeat(60));
    
    if (total2000Moves > 0) {
      console.log(`🎉 20X POTENTIAL CONFIRMED!`);
      console.log(`   Found ${total2000Moves} opportunities for 2000%+ gains`);
      console.log(`   Maximum move: ${maxMove.toFixed(2)}%`);
      
      console.log(`\n🎯 SINGLE-TRADE 20X STRATEGY:`);
      console.log(`   1. Wait for extreme market conditions`);
      console.log(`   2. Use maximum position size (90-100%)`);
      console.log(`   3. Apply high leverage (50-100x)`);
      console.log(`   4. Target: 2000%+ gain in one trade`);
      console.log(`   5. Stop loss: 5% maximum`);
      
    } else if (maxMove >= 1000) {
      console.log(`🚀 TWO-TRADE 20X STRATEGY:`);
      console.log(`   Maximum single move: ${maxMove.toFixed(2)}%`);
      console.log(`   Strategy: Compound two ${(maxMove/2).toFixed(0)}% trades`);
      console.log(`   Trade 1: 40 USDT → ${(40 * (1 + maxMove/200)).toFixed(0)} USDT`);
      console.log(`   Trade 2: ${(40 * (1 + maxMove/200)).toFixed(0)} USDT → ${(40 * Math.pow(1 + maxMove/200, 2)).toFixed(0)} USDT`);
      
    } else if (maxMove >= 500) {
      console.log(`📈 FOUR-TRADE 20X STRATEGY:`);
      console.log(`   Maximum single move: ${maxMove.toFixed(2)}%`);
      const singleTradeTarget = maxMove / 4;
      const finalAmount = 40 * Math.pow(1 + singleTradeTarget/100, 4);
      console.log(`   Each trade targets: ${singleTradeTarget.toFixed(0)}%`);
      console.log(`   Final result: ${finalAmount.toFixed(0)} USDT (${(finalAmount/40).toFixed(1)}x)`);
      
    } else {
      console.log(`⚠️  20X NOT ACHIEVABLE WITH CURRENT DATA:`);
      console.log(`   Maximum move found: ${maxMove.toFixed(2)}%`);
      console.log(`   Recommended target: ${Math.floor(maxMove/100)}x returns`);
    }
    
    console.log(`\n🔧 OPTIMAL PARAMETERS:`);
    console.log(`   Best dataset: ${bestDataset.file}`);
    console.log(`   Entry signals:`);
    console.log(`     • Volume spike (5x+ average)`);
    console.log(`     • Price breakout with momentum`);
    console.log(`     • RSI extreme levels (< 20 or > 80)`);
    console.log(`     • High volatility period`);
    console.log(`   Position sizing: 80-100% of capital`);
    console.log(`   Leverage: ${Math.min(100, Math.ceil(2000/maxMove))}x`);
    console.log(`   Stop loss: 3-5%`);
    console.log(`   Take profit: ${Math.min(maxMove, 2000).toFixed(0)}%`);
    
    if (total2000Moves > 0) {
      console.log(`\n✅ CONCLUSION: 20X IS POSSIBLE!`);
      console.log(`   The data confirms ${total2000Moves} opportunities for 20x returns`);
      console.log(`   Focus on the best dataset: ${bestDataset.file}`);
      console.log(`   Wait for extreme market conditions and go all-in`);
    } else {
      console.log(`\n📊 CONCLUSION: Adjust expectations`);
      console.log(`   20x requires perfect conditions not found in this data`);
      console.log(`   Realistic target: ${Math.floor(maxMove/100)}x returns`);
      console.log(`   Consider longer timeframes or different markets`);
    }
  }
}

// Run analysis
async function main() {
  const analyzer = new DataDrivenStrategy();
  await analyzer.analyzeAllDatasets();
}

main().catch(console.error);
