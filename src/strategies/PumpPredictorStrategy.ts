#!/usr/bin/env node

import fs from "fs";
import csv from "csv-parser";
import { RSI, EMA, SMA } from "technicalindicators";

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface PumpSignal {
  time: string;
  entryPrice: number;
  pumpStartPrice: number;
  pumpEndPrice: number;
  maxGain: number;
  duration: number;
  signals: string[];
  rsi: number;
  volume: number;
  volumeRatio: number;
  priceAction: string;
}

class PumpPredictor {
  private data: CandleData[] = [];

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          resolve();
        })
        .on("error", reject);
    });
  }

  private calculateIndicators(index: number): any {
    const lookback = Math.min(50, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);
    
    const closes = candles.map(c => c.close);
    const volumes = candles.map(c => c.volume);

    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 8, values: closes });
    const emaSlowValues = EMA.calculate({ period: 21, values: closes });

    const avgVolume = volumes.slice(-20).reduce((sum, vol) => sum + vol, 0) / Math.min(20, volumes.length);

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast: emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      avgVolume
    };
  }

  private findBiggestPumps(): PumpSignal[] {
    const pumps: PumpSignal[] = [];
    const minPumpPercent = 3; // Look for 3%+ pumps (achievable with 125x leverage)

    console.log("🔍 Scanning for BIGGEST PUMPS in the data...");

    for (let i = 50; i < this.data.length - 20; i++) {
      const currentCandle = this.data[i];
      const indicators = this.calculateIndicators(i);
      
      // Look ahead to find pumps that happen AFTER this point
      let maxGain = 0;
      let pumpEndPrice = currentCandle.close;
      let pumpDuration = 0;
      
      // Check next 20 candles (5 hours) for pumps
      for (let j = i + 1; j < Math.min(i + 21, this.data.length); j++) {
        const futureCandle = this.data[j];
        const gain = (futureCandle.high - currentCandle.close) / currentCandle.close * 100;
        
        if (gain > maxGain) {
          maxGain = gain;
          pumpEndPrice = futureCandle.high;
          pumpDuration = j - i;
        }
      }

      // If we found a significant pump, analyze the entry conditions
      if (maxGain >= minPumpPercent) {
        const signals = this.analyzePumpConditions(i, indicators, currentCandle);
        
        pumps.push({
          time: currentCandle.time,
          entryPrice: currentCandle.close,
          pumpStartPrice: currentCandle.close,
          pumpEndPrice,
          maxGain,
          duration: pumpDuration,
          signals,
          rsi: indicators.rsi,
          volume: currentCandle.volume,
          volumeRatio: currentCandle.volume / indicators.avgVolume,
          priceAction: this.analyzePriceAction(i)
        });
      }
    }

    // Sort by biggest gains first
    return pumps.sort((a, b) => b.maxGain - a.maxGain);
  }

  private analyzePumpConditions(index: number, indicators: any, candle: CandleData): string[] {
    const signals: string[] = [];
    
    // RSI conditions
    if (indicators.rsi < 25) signals.push("RSI_OVERSOLD");
    if (indicators.rsi > 25 && indicators.rsi < 40) signals.push("RSI_BULLISH_ZONE");
    
    // Volume conditions
    const volumeRatio = candle.volume / indicators.avgVolume;
    if (volumeRatio > 3) signals.push("VOLUME_EXPLOSION");
    if (volumeRatio > 2) signals.push("HIGH_VOLUME");
    if (volumeRatio > 1.5) signals.push("ABOVE_AVG_VOLUME");
    
    // Price action
    const priceChange = (candle.close - candle.open) / candle.open * 100;
    if (priceChange > 1) signals.push("STRONG_GREEN_CANDLE");
    if (priceChange > 0.5) signals.push("GREEN_CANDLE");
    
    // EMA conditions
    if (candle.close > indicators.emaFast) signals.push("ABOVE_EMA8");
    if (indicators.emaFast > indicators.emaSlow) signals.push("EMA_BULLISH");
    
    // Breakout conditions
    const recentHigh = Math.max(...this.data.slice(Math.max(0, index - 10), index).map(c => c.high));
    if (candle.high > recentHigh) signals.push("BREAKOUT_HIGH");
    
    return signals;
  }

  private analyzePriceAction(index: number): string {
    const candle = this.data[index];
    const prevCandle = this.data[index - 1];
    
    const bodySize = Math.abs(candle.close - candle.open) / candle.open * 100;
    const wickSize = (candle.high - Math.max(candle.open, candle.close)) / candle.open * 100;
    
    if (bodySize > 1 && candle.close > candle.open) return "STRONG_BULLISH";
    if (bodySize > 0.5 && candle.close > candle.open) return "BULLISH";
    if (wickSize > 0.5) return "REJECTION_WICK";
    
    return "NEUTRAL";
  }

  private createPumpModel(pumps: PumpSignal[]): any {
    console.log("\n🧠 CREATING PUMP PREDICTION MODEL...");
    
    // Analyze the most successful pump conditions
    const bigPumps = pumps.filter(p => p.maxGain >= 5); // 5%+ pumps
    
    if (bigPumps.length === 0) {
      console.log("❌ No significant pumps found in data");
      return null;
    }

    // Find common patterns in big pumps
    const signalFrequency: { [key: string]: number } = {};
    const rsiRanges: number[] = [];
    const volumeRatios: number[] = [];
    
    bigPumps.forEach(pump => {
      pump.signals.forEach(signal => {
        signalFrequency[signal] = (signalFrequency[signal] || 0) + 1;
      });
      rsiRanges.push(pump.rsi);
      volumeRatios.push(pump.volumeRatio);
    });

    // Find the most predictive signals
    const totalBigPumps = bigPumps.length;
    const strongSignals = Object.entries(signalFrequency)
      .filter(([signal, count]) => count / totalBigPumps >= 0.6) // Present in 60%+ of big pumps
      .map(([signal, count]) => ({ signal, frequency: count / totalBigPumps }))
      .sort((a, b) => b.frequency - a.frequency);

    const avgRsi = rsiRanges.reduce((sum, rsi) => sum + rsi, 0) / rsiRanges.length;
    const avgVolumeRatio = volumeRatios.reduce((sum, ratio) => sum + ratio, 0) / volumeRatios.length;

    return {
      strongSignals,
      optimalRsiRange: [Math.min(...rsiRanges), Math.max(...rsiRanges)],
      avgRsi,
      avgVolumeRatio,
      minVolumeRatio: Math.min(...volumeRatios),
      bigPumpsFound: totalBigPumps,
      avgPumpSize: bigPumps.reduce((sum, p) => sum + p.maxGain, 0) / totalBigPumps
    };
  }

  private simulateHighLeverageTrading(pumps: PumpSignal[], model: any): any {
    console.log("\n💰 SIMULATING 125X LEVERAGE TRADING...");
    
    const LEVERAGE = 125;
    const MARGIN = 20; // 20 USDT margin
    const POSITION_SIZE = MARGIN * LEVERAGE; // 2500 USDT position
    
    let balance = 100; // Starting with 100 USDT
    let successfulTrades = 0;
    let totalTrades = 0;
    const trades: any[] = [];

    // Test the model on historical pumps
    pumps.forEach(pump => {
      // Check if this pump matches our model criteria
      const matchesModel = this.doesPumpMatchModel(pump, model);
      
      if (matchesModel && balance >= MARGIN) {
        totalTrades++;
        
        // Calculate profit from the pump
        const pumpGainPercent = pump.maxGain / 100;
        const leveragedGain = pumpGainPercent * LEVERAGE;
        const profit = MARGIN * leveragedGain;
        
        balance += profit;
        successfulTrades++;
        
        trades.push({
          time: pump.time,
          entryPrice: pump.entryPrice,
          pumpGain: pump.maxGain,
          leveragedGain: leveragedGain * 100,
          profit,
          newBalance: balance,
          signals: pump.signals
        });

        console.log(`🚀 PUMP TRADE: ${pump.maxGain.toFixed(2)}% pump = ${(leveragedGain * 100).toFixed(0)}% leveraged gain = +${profit.toFixed(2)} USDT`);
      }
    });

    return {
      initialBalance: 100,
      finalBalance: balance,
      totalReturn: balance - 100,
      returnMultiplier: balance / 100,
      totalTrades,
      successfulTrades,
      successRate: totalTrades > 0 ? successfulTrades / totalTrades * 100 : 0,
      trades
    };
  }

  private doesPumpMatchModel(pump: PumpSignal, model: any): boolean {
    if (!model) return false;
    
    // Check if RSI is in optimal range
    const rsiInRange = pump.rsi >= model.optimalRsiRange[0] && pump.rsi <= model.optimalRsiRange[1];
    
    // Check if volume is sufficient
    const volumeOk = pump.volumeRatio >= model.minVolumeRatio;
    
    // Check if it has strong signals
    const hasStrongSignals = model.strongSignals.some((s: any) => 
      pump.signals.includes(s.signal)
    );
    
    return rsiInRange && volumeOk && hasStrongSignals;
  }

  async analyzePumpsInDataset(filePath: string): Promise<void> {
    console.log(`\n🔍 ANALYZING PUMPS IN: ${filePath}`);
    console.log("=" .repeat(60));
    
    await this.loadData(filePath);
    
    // Find all the biggest pumps
    const pumps = this.findBiggestPumps();
    
    console.log(`\n📊 PUMP ANALYSIS RESULTS:`);
    console.log(`   Total pumps found: ${pumps.length}`);
    
    if (pumps.length === 0) {
      console.log("   ❌ No significant pumps found in this dataset");
      return;
    }

    // Show top 10 biggest pumps
    console.log(`\n🚀 TOP 10 BIGGEST PUMPS:`);
    pumps.slice(0, 10).forEach((pump, index) => {
      console.log(`   ${index + 1}. ${pump.maxGain.toFixed(2)}% pump at ${pump.time}`);
      console.log(`      Entry: $${pump.entryPrice.toFixed(2)} → Peak: $${pump.pumpEndPrice.toFixed(2)}`);
      console.log(`      RSI: ${pump.rsi.toFixed(1)} | Volume: ${pump.volumeRatio.toFixed(1)}x avg`);
      console.log(`      Signals: ${pump.signals.join(', ')}`);
      console.log("");
    });

    // Create prediction model
    const model = this.createPumpModel(pumps);
    
    if (model) {
      console.log(`\n🧠 PUMP PREDICTION MODEL:`);
      console.log(`   Big pumps found: ${model.bigPumpsFound}`);
      console.log(`   Average pump size: ${model.avgPumpSize.toFixed(2)}%`);
      console.log(`   Optimal RSI range: ${model.optimalRsiRange[0].toFixed(1)} - ${model.optimalRsiRange[1].toFixed(1)}`);
      console.log(`   Average volume ratio: ${model.avgVolumeRatio.toFixed(1)}x`);
      
      console.log(`\n🎯 STRONGEST PUMP SIGNALS:`);
      model.strongSignals.forEach((signal: any, index: number) => {
        console.log(`   ${index + 1}. ${signal.signal} (${(signal.frequency * 100).toFixed(1)}% of big pumps)`);
      });

      // Simulate high leverage trading
      const simulation = this.simulateHighLeverageTrading(pumps, model);
      
      console.log(`\n💰 125X LEVERAGE SIMULATION RESULTS:`);
      console.log(`   Initial balance: ${simulation.initialBalance} USDT`);
      console.log(`   Final balance: ${simulation.finalBalance.toFixed(2)} USDT`);
      console.log(`   Total return: ${simulation.totalReturn.toFixed(2)} USDT`);
      console.log(`   Return multiplier: ${simulation.returnMultiplier.toFixed(2)}x`);
      console.log(`   Successful trades: ${simulation.successfulTrades}/${simulation.totalTrades}`);
      console.log(`   Success rate: ${simulation.successRate.toFixed(1)}%`);

      if (simulation.returnMultiplier >= 5) {
        console.log(`\n🎉 SUCCESS! Model achieved ${simulation.returnMultiplier.toFixed(1)}x returns!`);
        console.log(`💡 Key insight: ${model.strongSignals[0]?.signal} is the strongest pump predictor`);
      }

      // Save the model and results
      const results = {
        dataset: filePath,
        pumps: pumps.slice(0, 20), // Top 20 pumps
        model,
        simulation,
        timestamp: new Date().toISOString()
      };

      const filename = `pump_analysis_${filePath.split('/').pop()?.replace('.csv', '')}.json`;
      fs.writeFileSync(filename, JSON.stringify(results, null, 2));
      console.log(`\n💾 Results saved to ${filename}`);
    }
  }

  async analyzeAllDatasets(): Promise<void> {
    console.log("🚀 PUMP PREDICTOR - FINDING BIGGEST PUMPS FOR 125X LEVERAGE");
    console.log("Looking for exact moments when market pumps the hardest");
    console.log("=" .repeat(80));

    const dataDir = "./data";
    const files = fs.readdirSync(dataDir).filter(file => file.endsWith('.csv'));
    
    const allResults: any[] = [];

    for (const file of files) {
      const filePath = `${dataDir}/${file}`;
      await this.analyzePumpsInDataset(filePath);
    }

    console.log("\n🎯 PUMP PREDICTION STRATEGY FOR 125X LEVERAGE:");
    console.log("=" .repeat(60));
    console.log("1. Monitor for RSI oversold conditions (< 30)");
    console.log("2. Wait for volume explosion (3x+ average)");
    console.log("3. Look for breakout above recent highs");
    console.log("4. Enter LONG with 125x leverage using 20 USDT margin");
    console.log("5. Target 3-5% pumps for 375-625% leveraged gains");
    console.log("6. Use tight stops (0.5-1%) to protect capital");
    console.log("\n💰 With this strategy, a 4% pump = 500% gain = 100 USDT profit!");
  }
}

// Run pump analysis
async function main() {
  const predictor = new PumpPredictor();
  await predictor.analyzeAllDatasets();
}

main().catch(console.error);
