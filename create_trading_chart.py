#!/usr/bin/env python3
"""
Trading Chart Generator for 20X Strategy Backtest Results
Creates comprehensive charts showing positions, P&L, and balance progression
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
from datetime import datetime
import seaborn as sns

# Set style for better looking charts
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Trade data extracted from console output
trades_data = [
    {"time": "2024-01-31T17:00:00", "action": "LONG", "price": 42099.61, "pnl": -11.18, "balance": 28.82, "reason": "ULTRA_STOP"},
    {"time": "2024-02-01T08:00:00", "action": "LONG", "price": 42172.87, "pnl": 754.09, "balance": 782.90, "reason": "RSI_REVERSAL"},
    {"time": "2024-02-05T12:00:00", "action": "LONG", "price": 49637.76, "pnl": -251.67, "balance": 531.23, "reason": "ULTRA_STOP"},
    {"time": "2024-02-05T18:00:00", "action": "LONG", "price": 49582.33, "pnl": -105.63, "balance": 425.60, "reason": "ULTRA_STOP"},
    {"time": "2024-02-06T06:00:00", "action": "LONG", "price": 49862.04, "pnl": -142.81, "balance": 282.80, "reason": "ULTRA_STOP"},
    {"time": "2024-02-06T12:00:00", "action": "SHORT", "price": 49789.99, "pnl": -78.31, "balance": 204.49, "reason": "ULTRA_STOP"},
    {"time": "2024-02-06T18:00:00", "action": "LONG", "price": 49898.00, "pnl": -36.46, "balance": 168.03, "reason": "ULTRA_STOP"},
    {"time": "2024-02-07T00:00:00", "action": "LONG", "price": 50050.00, "pnl": -32.35, "balance": 135.68, "reason": "ULTRA_STOP"},
    {"time": "2024-02-07T06:00:00", "action": "LONG", "price": 50096.40, "pnl": -75.75, "balance": 59.92, "reason": "ULTRA_STOP"},
    {"time": "2024-02-07T12:00:00", "action": "SHORT", "price": 49983.09, "pnl": -12.73, "balance": 47.19, "reason": "ULTRA_STOP"},
    {"time": "2024-02-07T18:00:00", "action": "LONG", "price": 50122.85, "pnl": -14.34, "balance": 32.85, "reason": "ULTRA_STOP"},
    {"time": "2024-02-08T00:00:00", "action": "LONG", "price": 50204.99, "pnl": -7.18, "balance": 25.67, "reason": "ULTRA_STOP"},
    {"time": "2024-02-08T06:00:00", "action": "SHORT", "price": 50088.43, "pnl": 117.89, "balance": 143.56, "reason": "RSI_REVERSAL"},
    {"time": "2024-02-08T12:00:00", "action": "SHORT", "price": 48944.01, "pnl": -110.41, "balance": 33.15, "reason": "ULTRA_STOP"},
    {"time": "2024-02-08T18:00:00", "action": "LONG", "price": 49000.00, "pnl": 189.33, "balance": 222.48, "reason": "RSI_REVERSAL"},
    {"time": "2024-02-09T00:00:00", "action": "LONG", "price": 51254.56, "pnl": 124.92, "balance": 347.40, "reason": "RSI_REVERSAL"},
    {"time": "2024-02-09T06:00:00", "action": "LONG", "price": 51551.10, "pnl": -68.65, "balance": 278.75, "reason": "ULTRA_STOP"},
    {"time": "2024-02-09T12:00:00", "action": "LONG", "price": 51311.43, "pnl": -81.57, "balance": 197.17, "reason": "ULTRA_STOP"},
    {"time": "2024-02-09T18:00:00", "action": "LONG", "price": 51396.02, "pnl": -57.77, "balance": 139.41, "reason": "ULTRA_STOP"},
    {"time": "2024-02-10T00:00:00", "action": "LONG", "price": 51555.12, "pnl": -38.13, "balance": 101.28, "reason": "ULTRA_STOP"},
    {"time": "2024-02-10T06:00:00", "action": "LONG", "price": 51652.00, "pnl": -23.04, "balance": 78.24, "reason": "ULTRA_STOP"},
    {"time": "2024-02-10T12:00:00", "action": "LONG", "price": 51685.99, "pnl": -36.88, "balance": 41.36, "reason": "ULTRA_STOP"},
    {"time": "2024-02-10T18:00:00", "action": "LONG", "price": 51843.98, "pnl": -13.54, "balance": 27.82, "reason": "ULTRA_STOP"},
    {"time": "2024-02-11T00:00:00", "action": "SHORT", "price": 51590.00, "pnl": -6.34, "balance": 21.48, "reason": "ULTRA_STOP"},
    {"time": "2024-02-11T06:00:00", "action": "SHORT", "price": 51581.35, "pnl": -6.61, "balance": 14.86, "reason": "ULTRA_STOP"},
    {"time": "2024-02-11T12:00:00", "action": "SHORT", "price": 51550.01, "pnl": -4.68, "balance": 10.18, "reason": "ULTRA_STOP"},
    {"time": "2024-02-11T18:00:00", "action": "SHORT", "price": 51448.55, "pnl": -2.51, "balance": 7.67, "reason": "ULTRA_STOP"},
    {"time": "2024-02-12T00:00:00", "action": "LONG", "price": 51678.00, "pnl": -2.88, "balance": 4.79, "reason": "ULTRA_STOP"},
    {"time": "2024-02-12T06:00:00", "action": "SHORT", "price": 51600.00, "pnl": -1.66, "balance": 3.13, "reason": "ULTRA_STOP"},
    {"time": "2024-02-12T12:00:00", "action": "LONG", "price": 51792.43, "pnl": -0.56, "balance": 2.58, "reason": "ULTRA_STOP"},
    {"time": "2024-02-12T18:00:00", "action": "SHORT", "price": 51776.61, "pnl": -1.17, "balance": 1.41, "reason": "ULTRA_STOP"},
    {"time": "2024-02-13T00:00:00", "action": "LONG", "price": 51910.42, "pnl": -0.59, "balance": 0.81, "reason": "ULTRA_STOP"},
    {"time": "2024-02-13T06:00:00", "action": "SHORT", "price": 52114.26, "pnl": -0.12, "balance": 0.69, "reason": "ULTRA_STOP"},
    {"time": "2024-02-13T12:00:00", "action": "LONG", "price": 52271.12, "pnl": -0.26, "balance": 0.43, "reason": "ULTRA_STOP"},
    {"time": "2024-02-13T18:00:00", "action": "LONG", "price": 52320.00, "pnl": -0.07, "balance": 0.36, "reason": "ULTRA_STOP"},
]

# Convert to DataFrame
df = pd.DataFrame(trades_data)
df['time'] = pd.to_datetime(df['time'])
df['cumulative_pnl'] = df['pnl'].cumsum()
df['trade_number'] = range(1, len(df) + 1)

# Create the comprehensive chart
fig = plt.figure(figsize=(20, 16))

# 1. Balance Progression Over Time
ax1 = plt.subplot(3, 2, 1)
plt.plot(df['time'], df['balance'], linewidth=3, color='#2E86AB', marker='o', markersize=4)
plt.axhline(y=40, color='gray', linestyle='--', alpha=0.7, label='Initial Balance')
plt.axhline(y=840, color='red', linestyle='--', alpha=0.7, label='21x Target (840 USDT)')
plt.fill_between(df['time'], df['balance'], alpha=0.3, color='#2E86AB')
plt.title('💰 Balance Progression Over Time', fontsize=14, fontweight='bold')
plt.ylabel('Balance (USDT)', fontsize=12)
plt.legend()
plt.grid(True, alpha=0.3)
plt.yscale('log')  # Log scale to better show the dramatic changes

# 2. Individual Trade P&L
ax2 = plt.subplot(3, 2, 2)
colors = ['green' if pnl > 0 else 'red' for pnl in df['pnl']]
bars = plt.bar(df['trade_number'], df['pnl'], color=colors, alpha=0.7)
plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
plt.title('📊 Individual Trade P&L', fontsize=14, fontweight='bold')
plt.xlabel('Trade Number', fontsize=12)
plt.ylabel('P&L (USDT)', fontsize=12)
plt.grid(True, alpha=0.3)

# Add value labels on significant bars
for i, (bar, pnl) in enumerate(zip(bars, df['pnl'])):
    if abs(pnl) > 50:  # Only label significant trades
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + (5 if pnl > 0 else -15), 
                f'{pnl:.0f}', ha='center', va='bottom' if pnl > 0 else 'top', fontweight='bold')

# 3. Cumulative P&L
ax3 = plt.subplot(3, 2, 3)
plt.plot(df['trade_number'], df['cumulative_pnl'], linewidth=3, color='#A23B72', marker='s', markersize=4)
plt.fill_between(df['trade_number'], df['cumulative_pnl'], alpha=0.3, color='#A23B72')
plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
plt.title('📈 Cumulative P&L Progression', fontsize=14, fontweight='bold')
plt.xlabel('Trade Number', fontsize=12)
plt.ylabel('Cumulative P&L (USDT)', fontsize=12)
plt.grid(True, alpha=0.3)

# 4. Position Types Distribution
ax4 = plt.subplot(3, 2, 4)
position_counts = df['action'].value_counts()
colors_pie = ['#F18F01', '#C73E1D']
wedges, texts, autotexts = plt.pie(position_counts.values, labels=position_counts.index, 
                                  autopct='%1.1f%%', colors=colors_pie, startangle=90)
plt.title('🎯 Position Types Distribution', fontsize=14, fontweight='bold')

# 5. Exit Reasons Analysis
ax5 = plt.subplot(3, 2, 5)
exit_reasons = df['reason'].value_counts()
bars = plt.bar(range(len(exit_reasons)), exit_reasons.values, 
               color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
plt.title('🚪 Exit Reasons Analysis', fontsize=14, fontweight='bold')
plt.xlabel('Exit Reason', fontsize=12)
plt.ylabel('Count', fontsize=12)
plt.xticks(range(len(exit_reasons)), exit_reasons.index, rotation=45, ha='right')
plt.grid(True, alpha=0.3)

# Add value labels on bars
for bar, value in zip(bars, exit_reasons.values):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
             str(value), ha='center', va='bottom', fontweight='bold')

# 6. Price vs Balance Correlation
ax6 = plt.subplot(3, 2, 6)
plt.scatter(df['price'], df['balance'], c=df['trade_number'], cmap='viridis', 
           s=100, alpha=0.7, edgecolors='black', linewidth=1)
plt.colorbar(label='Trade Number')
plt.title('💹 Price vs Balance Correlation', fontsize=14, fontweight='bold')
plt.xlabel('Entry Price (USDT)', fontsize=12)
plt.ylabel('Balance After Trade (USDT)', fontsize=12)
plt.grid(True, alpha=0.3)

# Add trend line
z = np.polyfit(df['price'], df['balance'], 1)
p = np.poly1d(z)
plt.plot(df['price'], p(df['price']), "r--", alpha=0.8, linewidth=2)

plt.tight_layout(pad=3.0)

# Add overall title and summary
fig.suptitle('🚀 ULTRA 20X STRATEGY - COMPREHENSIVE TRADING ANALYSIS\n' + 
             f'Dataset: history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv\n' +
             f'Initial: 40 USDT → Final: 0.36 USDT | Return: 0.01x | Trades: 35 | Win Rate: 11.4%', 
             fontsize=16, fontweight='bold', y=0.98)

# Save the chart
plt.savefig('results/reports/ultra_20x_trading_analysis.png', dpi=300, bbox_inches='tight')
plt.savefig('results/reports/ultra_20x_trading_analysis.pdf', bbox_inches='tight')

print("📊 Comprehensive trading charts created successfully!")
print("📁 Saved to:")
print("   - results/reports/ultra_20x_trading_analysis.png")
print("   - results/reports/ultra_20x_trading_analysis.pdf")

# Create a summary statistics table
print("\n" + "="*80)
print("📈 TRADING PERFORMANCE SUMMARY")
print("="*80)
print(f"Initial Balance:     40.00 USDT")
print(f"Final Balance:       0.36 USDT")
print(f"Total Return:        {((0.36/40)-1)*100:.2f}%")
print(f"Return Multiplier:   0.01x")
print(f"Target Achievement:  ❌ Failed (Target: 21x)")
print(f"Total Trades:        35")
print(f"Winning Trades:      {len(df[df['pnl'] > 0])}")
print(f"Losing Trades:       {len(df[df['pnl'] < 0])}")
print(f"Win Rate:            {(len(df[df['pnl'] > 0])/len(df))*100:.1f}%")
print(f"Best Trade:          +{df['pnl'].max():.2f} USDT")
print(f"Worst Trade:         {df['pnl'].min():.2f} USDT")
print(f"Average Trade:       {df['pnl'].mean():.2f} USDT")
print(f"Max Balance:         {df['balance'].max():.2f} USDT (Peak: {(df['balance'].max()/40):.2f}x)")
print(f"Max Drawdown:        {((40 - df['balance'].min())/40)*100:.1f}%")
print("="*80)

plt.show()
