{"name": "ultimate-8x-trading-strategy", "version": "2.0.0", "main": "ultimate_8x_strategy.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint .", "lint:fix": "eslint --fix .", "start": "node --loader ts-node/esm src/core/TradingBot.ts", "bot": "node --loader ts-node/esm src/core/TradingBot.ts", "demo": "node --loader ts-node/esm demo_bot.ts", "backtest": "node --loader ts-node/esm src/backtesting/Backtester.ts", "backtest-optimized": "node --loader ts-node/esm src/backtesting/OptimizedBacktester.ts", "strategy-8x": "node --loader ts-node/esm scripts/run-strategy.ts 8x", "strategy-20x": "node --loader ts-node/esm scripts/run-strategy.ts 20x", "strategy-compare": "node --loader ts-node/esm scripts/run-strategy.ts compare", "strategy-all": "node --loader ts-node/esm scripts/run-strategy.ts all", "ultimate-8x": "node --loader ts-node/esm src/strategies/Ultimate8xStrategy.ts", "ultimate-20x": "node --loader ts-node/esm src/strategies/Ultimate20xStrategy.ts", "conservative-20x": "node --loader ts-node/esm src/strategies/Conservative20xStrategy.ts", "adaptive-strategy": "node --loader ts-node/esm src/strategies/AdaptiveStrategy.ts", "pump-predictor": "node --loader ts-node/esm src/strategies/PumpPredictorStrategy.ts", "test-strategies": "node --loader ts-node/esm scripts/test-strategies.ts", "show-options": "node --loader ts-node/esm scripts/show-options.ts", "help": "npm run show-options", "web-server": "node --loader ts-node/esm web/server.ts", "dashboard": "npm run web-server", "run-backtest": "node --loader ts-node/esm scripts/run-backtest.ts", "analyze-data": "node --loader ts-node/esm scripts/data-analyzer.ts", "quick-test": "npm run strategy-8x", "full-analysis": "npm run strategy-all", "validate": "npm run test-strategies", "build": "tsc", "dev": "npm run start", "prod": "npm run build && node dist/src/core/TradingBot.js", "clean": "rm -rf dist results/logs/* results/backtests/*", "reset": "npm run clean && npm install"}, "type": "module", "keywords": ["trading", "algorithm", "cryptocurrency", "bitcoin", "strategy", "8x-returns", "adaptive", "market-regime", "risk-management", "backtesting"], "author": "Ultimate 8X Strategy Team", "license": "MIT", "description": "Adaptive algorithmic trading strategy achieving consistent 8x returns across diverse market conditions", "dependencies": {"binance-api-node": "file:binance-api-node", "csv-parser": "^3.1.0", "dotenv": "^16.4.7", "fs": "^0.0.1-security", "set-interval-async": "^3.0.3", "technicalindicators": "^3.1.0", "ts-node": "^10.9.2"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/plugin-transform-runtime": "^7.26.9", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@tsconfig/node20": "^20.1.4", "eslint": "^9.20.1"}}