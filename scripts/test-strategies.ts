#!/usr/bin/env node

import { Ultimate8xStrategy } from "./ultimate_8x_strategy.js";
import { Ultimate20xStrategy } from "./ultimate_20x_strategy.js";
import { HTMLReportGenerator } from "./html_report_generator.js";
import fs from "fs";

async function main() {
  console.log("🚀 ULTIMATE TRADING STRATEGY TEST SUITE");
  console.log("=" .repeat(60));
  console.log("Testing both 8X and 20X strategies on historical data");
  console.log("=" .repeat(60));

  // Get the first available data file
  const dataDir = "./data";
  let dataFiles: string[] = [];
  
  try {
    const files = fs.readdirSync(dataDir);
    dataFiles = files
      .filter(file => file.endsWith('.csv'))
      .map(file => `${dataDir}/${file}`);
  } catch (error) {
    console.error("❌ Error loading data files:", error);
    process.exit(1);
  }

  if (dataFiles.length === 0) {
    console.error("❌ No CSV data files found in ./data directory");
    process.exit(1);
  }

  const testFile = dataFiles[0]; // Use first available file
  console.log(`📊 Testing on dataset: ${testFile}`);
  console.log("");

  try {
    // Test 8X Strategy
    console.log("🎯 Testing 8X Strategy...");
    console.log("-".repeat(40));
    const strategy8x = new Ultimate8xStrategy(40);
    await strategy8x.loadData(testFile);
    await strategy8x.runBacktest();
    const results8x = strategy8x.getResults();

    console.log("\n🚀 Testing 20X Strategy...");
    console.log("-".repeat(40));
    const strategy20x = new Ultimate20xStrategy(40);
    await strategy20x.loadData(testFile);
    await strategy20x.runBacktest();
    const results20x = strategy20x.getResults();

    // Compare results
    console.log("\n🏆 STRATEGY COMPARISON");
    console.log("=" .repeat(60));
    
    const performance8x = results8x.returnMultiplier;
    const performance20x = results20x.returnMultiplier;
    
    let winner = "TIE";
    if (results8x.targetAchieved && !results20x.targetAchieved) {
      winner = "8X Strategy";
    } else if (results20x.targetAchieved && !results8x.targetAchieved) {
      winner = "20X Strategy";
    } else if (results8x.targetAchieved && results20x.targetAchieved) {
      winner = results8x.finalBalance > results20x.finalBalance ? "8X Strategy" : "20X Strategy";
    } else {
      winner = performance8x > performance20x ? "8X Strategy" : "20X Strategy";
    }

    const comparison = {
      winner,
      performance8x,
      performance20x,
      trades8x: results8x.totalTrades,
      trades20x: results20x.totalTrades,
      winRate8x: results8x.winRate,
      winRate20x: results20x.winRate,
      maxDrawdown8x: results8x.maxDrawdown,
      maxDrawdown20x: results20x.maxDrawdown,
    };

    // Print comparison
    console.log(`📊 Performance Comparison:`);
    console.log(`   8X Strategy:  ${results8x.finalBalance.toFixed(2)} USDT (${performance8x.toFixed(2)}x)`);
    console.log(`   20X Strategy: ${results20x.finalBalance.toFixed(2)} USDT (${performance20x.toFixed(2)}x)`);
    console.log(`   Winner:       ${winner}`);
    
    console.log(`\n🎯 Target Achievement:`);
    console.log(`   8X Target (8x):   ${results8x.targetAchieved ? "✅ ACHIEVED" : "❌ Not reached"}`);
    console.log(`   20X Target (21x): ${results20x.targetAchieved ? "✅ ACHIEVED" : "❌ Not reached"}`);
    
    console.log(`\n📈 Trading Metrics:`);
    console.log(`   Total Trades:     8X: ${comparison.trades8x} | 20X: ${comparison.trades20x}`);
    console.log(`   Win Rate:         8X: ${comparison.winRate8x.toFixed(1)}% | 20X: ${comparison.winRate20x.toFixed(1)}%`);
    console.log(`   Max Drawdown:     8X: ${comparison.maxDrawdown8x.toFixed(2)}% | 20X: ${comparison.maxDrawdown20x.toFixed(2)}%`);

    // Save results
    const combinedResults = {
      timestamp: new Date().toISOString(),
      dataFile: testFile,
      strategy8x: results8x,
      strategy20x: results20x,
      comparison: comparison
    };

    const resultsFile = "combined_strategy_results.json";
    fs.writeFileSync(resultsFile, JSON.stringify(combinedResults, null, 2));
    console.log(`\n💾 Results saved to ${resultsFile}`);

    // Generate HTML Report
    console.log("\n📄 Generating HTML Report...");
    const reportGenerator = new HTMLReportGenerator();
    await reportGenerator.generateFromResults(resultsFile, "strategy_report.html");

    // Final summary
    console.log("\n" + "=" .repeat(60));
    console.log("🎉 TESTING COMPLETE!");
    console.log("=" .repeat(60));
    
    if (results20x.targetAchieved) {
      console.log("🚀 20X Strategy achieved the ultimate 21x target!");
      console.log("💎 Recommendation: Use 20X strategy for maximum returns");
    } else if (results8x.targetAchieved) {
      console.log("🎯 8X Strategy achieved its 8x target reliably");
      console.log("🛡️ Recommendation: Use 8X strategy for consistent performance");
    } else {
      const better = performance8x > performance20x ? "8X" : "20X";
      console.log(`📈 ${better} Strategy showed better performance`);
      console.log("🔄 Recommendation: Consider testing on different market conditions");
    }
    
    console.log("\n📊 View detailed results:");
    console.log(`   📄 HTML Report: strategy_report.html`);
    console.log(`   📋 JSON Data: ${resultsFile}`);
    console.log("\n🚀 Ready for live trading with your chosen strategy!");
    console.log("=" .repeat(60));

  } catch (error) {
    console.error("❌ Error during testing:", error);
    process.exit(1);
  }
}

// Run the test
main().catch(console.error);
