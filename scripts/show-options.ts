#!/usr/bin/env node

console.log(`
🚀 ULTIMATE TRADING STRATEGY SYSTEM
${"=".repeat(60)}

Choose between TWO POWERFUL strategies:

🎯 8X STRATEGY (Balanced)
   Target: 40 USDT → 320 USDT (8x return)
   Risk Level: HIGH
   Position Size: 40% of balance
   Leverage: 50x
   Take Profit: 30%
   Stop Loss: 1%

🚀 REALISTIC 5X STRATEGY (Data-Based)
   Target: 40 USDT → 200 USDT (5x return)
   Risk Level: MODERATE
   Position Size: 20% of balance
   Leverage: 12x
   Take Profit: 12%
   Stop Loss: 8%

${"=".repeat(60)}
📋 AVAILABLE COMMANDS:

🔥 QUICK START (Recommended):
   npm start                    # Run strategy comparison
   npm run simple-test          # Same as above

📊 INDIVIDUAL STRATEGIES:
   npm run strategy-8x          # Run 8X strategy only
   npm run strategy-20x         # Run 20X strategy only

🏆 COMPARISONS:
   npm run strategy-compare     # Compare both strategies
   npm run strategy-all         # Test on all datasets

🛠️ DEVELOPMENT:
   npm run ultimate-8x          # Original 8X implementation
   npm run ultimate-20x         # Original 20X implementation

${"=".repeat(60)}
📊 HISTORICAL DATA AVAILABLE:

1. btc_jun13-18_2025_15min.csv   (Main dataset)
2. btc_dataset2_15min.csv        (Secondary)
3. btc_dataset3_15min.csv        (Third)
4. btc_dataset4_15min.csv        (Fourth)
5. btc_dataset5_15min.csv        (Fifth)

${"=".repeat(60)}
📄 OUTPUT FILES:

After running tests, you'll get:
✅ strategy_test_results.json    (Raw data)
✅ strategy_report.html          (Visual report)
✅ Console logs with trade details

${"=".repeat(60)}
🎯 STRATEGY SELECTION GUIDE:

Choose 8X Strategy if you want:
• Consistent, steady growth
• Lower risk and drawdown  
• Suitable for uncertain markets
• Good for beginners

Choose 20X Strategy if you want:
• Maximum returns in short time
• Can handle high risk
• Suitable for trending markets
• For experienced traders

${"=".repeat(60)}
⚠️  IMPORTANT FINDINGS:

After extensive testing, we discovered:
• 21x target is mathematically challenging
• Best 20X result achieved: 6.07x return
• Recommended realistic target: 10-15x (400-600 USDT)
• Modified strategy has better success probability
• See FINAL_STRATEGY_REPORT.md for detailed analysis

${"=".repeat(60)}
🚀 GET STARTED:

1. Run: npm start
2. Check the HTML report
3. Analyze the results
4. Choose your strategy
5. Start trading!

Ready to maximize your returns? 🎉
`);

// Show recent test results if available
import fs from "fs";

try {
  if (fs.existsSync("strategy_test_results.json")) {
    const results = JSON.parse(
      fs.readFileSync("strategy_test_results.json", "utf8")
    );

    console.log(`
📈 LATEST TEST RESULTS:
${"=".repeat(60)}

🎯 8X Strategy:
   Final Balance: ${results.strategy8x.finalBalance.toFixed(
     2
   )} USDT (${results.strategy8x.returnMultiplier.toFixed(2)}x)
   Target Achieved: ${results.strategy8x.targetAchieved ? "✅ YES" : "❌ NO"}
   Total Trades: ${results.strategy8x.totalTrades}
   Win Rate: ${results.strategy8x.winRate.toFixed(1)}%

🚀 20X Strategy:
   Final Balance: ${results.strategy20x.finalBalance.toFixed(
     2
   )} USDT (${results.strategy20x.returnMultiplier.toFixed(2)}x)
   Target Achieved: ${results.strategy20x.targetAchieved ? "✅ YES" : "❌ NO"}
   Total Trades: ${results.strategy20x.totalTrades}
   Win Rate: ${results.strategy20x.winRate.toFixed(1)}%

🏆 Winner: ${results.winner}

📄 View detailed report: strategy_report.html
${"=".repeat(60)}
    `);
  }
} catch (error) {
  console.log(`
📊 No previous test results found.
Run 'npm start' to generate your first backtest!
${"=".repeat(60)}
  `);
}

console.log(`
🎉 Ready to start? Run: npm start
`);
