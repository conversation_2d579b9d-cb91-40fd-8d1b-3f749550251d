#!/usr/bin/env node

/**
 * Data analyzer script for market data analysis
 * Usage: npm run analyze-data [dataset]
 */

import * as fs from 'fs';
import * as path from 'path';
import * as csv from 'csv-parser';
import { CandleData, MarketState } from '../src/types/market.js';
import { IndicatorCalculator } from '../src/utils/indicators.js';
import { Helpers } from '../src/utils/helpers.js';
import { Logger } from '../src/utils/logger.js';

interface DatasetAnalysis {
  name: string;
  totalCandles: number;
  timeRange: {
    start: string;
    end: string;
    duration: string;
  };
  priceAnalysis: {
    minPrice: number;
    maxPrice: number;
    avgPrice: number;
    priceRange: number;
    volatility: number;
  };
  volumeAnalysis: {
    minVolume: number;
    maxVolume: number;
    avgVolume: number;
    totalVolume: number;
  };
  marketRegimes: {
    [regime: string]: {
      count: number;
      percentage: number;
    };
  };
  patterns: {
    [pattern: string]: number;
  };
  qualityScore: number;
  recommendations: string[];
}

class DataAnalyzer {
  private logger = Logger.getInstance();

  async analyzeDataset(filePath: string): Promise<DatasetAnalysis> {
    this.logger.info(`📊 Analyzing dataset: ${path.basename(filePath)}`);

    const data = await this.loadData(filePath);
    
    if (data.length === 0) {
      throw new Error('No data found in dataset');
    }

    const analysis: DatasetAnalysis = {
      name: path.basename(filePath),
      totalCandles: data.length,
      timeRange: this.analyzeTimeRange(data),
      priceAnalysis: this.analyzePrices(data),
      volumeAnalysis: this.analyzeVolume(data),
      marketRegimes: this.analyzeMarketRegimes(data),
      patterns: this.analyzePatterns(data),
      qualityScore: 0,
      recommendations: []
    };

    analysis.qualityScore = this.calculateQualityScore(analysis);
    analysis.recommendations = this.generateRecommendations(analysis);

    return analysis;
  }

  private async loadData(filePath: string): Promise<CandleData[]> {
    return new Promise((resolve, reject) => {
      const data: CandleData[] = [];
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
          try {
            data.push({
              time: row.time || row.timestamp,
              open: parseFloat(row.open),
              high: parseFloat(row.high),
              low: parseFloat(row.low),
              close: parseFloat(row.close),
              volume: parseFloat(row.volume)
            });
          } catch (error) {
            // Skip invalid rows
          }
        })
        .on('end', () => resolve(data))
        .on('error', reject);
    });
  }

  private analyzeTimeRange(data: CandleData[]) {
    const start = data[0].time;
    const end = data[data.length - 1].time;
    const startDate = new Date(start);
    const endDate = new Date(end);
    const duration = Helpers.formatDuration(endDate.getTime() - startDate.getTime());

    return { start, end, duration };
  }

  private analyzePrices(data: CandleData[]) {
    const closes = data.map(d => d.close);
    const highs = data.map(d => d.high);
    const lows = data.map(d => d.low);
    
    const minPrice = Math.min(...lows);
    const maxPrice = Math.max(...highs);
    const avgPrice = closes.reduce((sum, price) => sum + price, 0) / closes.length;
    const priceRange = maxPrice - minPrice;
    const volatility = IndicatorCalculator.calculateVolatility(closes);

    return {
      minPrice,
      maxPrice,
      avgPrice,
      priceRange,
      volatility
    };
  }

  private analyzeVolume(data: CandleData[]) {
    const volumes = data.map(d => d.volume);
    
    return {
      minVolume: Math.min(...volumes),
      maxVolume: Math.max(...volumes),
      avgVolume: volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length,
      totalVolume: volumes.reduce((sum, vol) => sum + vol, 0)
    };
  }

  private analyzeMarketRegimes(data: CandleData[]): { [regime: string]: { count: number; percentage: number } } {
    const regimes = { EXPLOSIVE: 0, TRENDING: 0, CHOPPY: 0, DEAD: 0 };
    
    for (let i = 50; i < data.length; i++) {
      const indicators = IndicatorCalculator.calculateAllIndicators(data, i);
      if (!indicators) continue;
      
      const regime = this.determineMarketRegime(data, i, indicators);
      regimes[regime]++;
    }

    const total = Object.values(regimes).reduce((sum, count) => sum + count, 0);
    
    return Object.entries(regimes).reduce((acc, [regime, count]) => {
      acc[regime] = {
        count,
        percentage: total > 0 ? (count / total) * 100 : 0
      };
      return acc;
    }, {} as any);
  }

  private determineMarketRegime(data: CandleData[], index: number, indicators: any): keyof typeof MarketState.prototype {
    const recentCandles = data.slice(Math.max(0, index - 20), index + 1);
    const volatility = this.calculateRecentVolatility(recentCandles);
    const volumeRatio = indicators.volumeRatio;
    
    if (volatility > 0.05 && volumeRatio > 3) return 'EXPLOSIVE';
    if (volatility > 0.03 && volumeRatio > 1.5) return 'TRENDING';
    if (volatility > 0.015) return 'CHOPPY';
    return 'DEAD';
  }

  private calculateRecentVolatility(candles: CandleData[]): number {
    if (candles.length < 2) return 0;
    
    const returns = [];
    for (let i = 1; i < candles.length; i++) {
      const returnValue = (candles[i].close - candles[i - 1].close) / candles[i - 1].close;
      returns.push(Math.abs(returnValue));
    }
    
    return returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
  }

  private analyzePatterns(data: CandleData[]): { [pattern: string]: number } {
    const patterns: { [pattern: string]: number } = {};
    
    for (let i = 5; i < data.length; i++) {
      const detectedPatterns = IndicatorCalculator.detectPatterns(data, i);
      
      for (const pattern of detectedPatterns) {
        patterns[pattern] = (patterns[pattern] || 0) + 1;
      }
    }
    
    return patterns;
  }

  private calculateQualityScore(analysis: DatasetAnalysis): number {
    let score = 0;
    
    // Data completeness (0-25 points)
    if (analysis.totalCandles > 1000) score += 25;
    else if (analysis.totalCandles > 500) score += 15;
    else if (analysis.totalCandles > 100) score += 10;
    
    // Volatility appropriateness (0-25 points)
    const volatility = analysis.priceAnalysis.volatility;
    if (volatility > 0.02 && volatility < 0.1) score += 25;
    else if (volatility > 0.01 && volatility < 0.15) score += 15;
    else score += 5;
    
    // Market regime diversity (0-25 points)
    const regimeCount = Object.values(analysis.marketRegimes).filter(r => r.percentage > 5).length;
    score += regimeCount * 6.25;
    
    // Pattern richness (0-25 points)
    const patternCount = Object.keys(analysis.patterns).length;
    if (patternCount > 3) score += 25;
    else if (patternCount > 1) score += 15;
    else score += 5;
    
    return Math.min(100, Math.round(score));
  }

  private generateRecommendations(analysis: DatasetAnalysis): string[] {
    const recommendations: string[] = [];
    
    if (analysis.totalCandles < 500) {
      recommendations.push('Dataset is small - consider using for quick tests only');
    }
    
    if (analysis.priceAnalysis.volatility > 0.15) {
      recommendations.push('High volatility dataset - good for testing extreme market conditions');
    } else if (analysis.priceAnalysis.volatility < 0.01) {
      recommendations.push('Low volatility dataset - may not provide sufficient trading opportunities');
    }
    
    const explosivePercentage = analysis.marketRegimes.EXPLOSIVE?.percentage || 0;
    if (explosivePercentage > 30) {
      recommendations.push('High explosive market periods - excellent for aggressive strategies');
    } else if (explosivePercentage < 5) {
      recommendations.push('Few explosive periods - may not be suitable for high-return strategies');
    }
    
    if (analysis.qualityScore > 80) {
      recommendations.push('High quality dataset - excellent for strategy development');
    } else if (analysis.qualityScore < 50) {
      recommendations.push('Lower quality dataset - use with caution for strategy validation');
    }
    
    return recommendations;
  }

  async analyzeAllDatasets(): Promise<void> {
    const dataDir = './data/datasets';
    const files = fs.readdirSync(dataDir).filter(f => f.endsWith('.csv'));
    
    if (files.length === 0) {
      this.logger.warn('No CSV files found in data/datasets directory');
      return;
    }
    
    const analyses: DatasetAnalysis[] = [];
    
    for (const file of files) {
      try {
        const filePath = path.join(dataDir, file);
        const analysis = await this.analyzeDataset(filePath);
        analyses.push(analysis);
        
        this.logAnalysis(analysis);
      } catch (error) {
        this.logger.error(`Failed to analyze ${file}:`, error);
      }
    }
    
    // Save combined analysis
    const outputPath = './results/backtests/dataset_analysis.json';
    Helpers.saveJSON(analyses, outputPath);
    this.logger.info(`\n💾 Analysis saved to: ${outputPath}`);
    
    // Summary
    this.logSummary(analyses);
  }

  private logAnalysis(analysis: DatasetAnalysis): void {
    this.logger.info(`\n📊 ${analysis.name}`);
    this.logger.info(`${'='.repeat(50)}`);
    this.logger.info(`  Candles: ${analysis.totalCandles}`);
    this.logger.info(`  Duration: ${analysis.timeRange.duration}`);
    this.logger.info(`  Price Range: $${analysis.priceAnalysis.minPrice.toFixed(2)} - $${analysis.priceAnalysis.maxPrice.toFixed(2)}`);
    this.logger.info(`  Volatility: ${(analysis.priceAnalysis.volatility * 100).toFixed(2)}%`);
    this.logger.info(`  Quality Score: ${analysis.qualityScore}/100`);
    
    this.logger.info(`  Market Regimes:`);
    Object.entries(analysis.marketRegimes).forEach(([regime, data]) => {
      this.logger.info(`    ${regime}: ${data.percentage.toFixed(1)}%`);
    });
    
    if (analysis.recommendations.length > 0) {
      this.logger.info(`  Recommendations:`);
      analysis.recommendations.forEach(rec => {
        this.logger.info(`    • ${rec}`);
      });
    }
  }

  private logSummary(analyses: DatasetAnalysis[]): void {
    this.logger.info(`\n🎯 DATASET ANALYSIS SUMMARY`);
    this.logger.info(`${'='.repeat(50)}`);
    
    const avgQuality = analyses.reduce((sum, a) => sum + a.qualityScore, 0) / analyses.length;
    const highQuality = analyses.filter(a => a.qualityScore > 80);
    const bestDataset = analyses.reduce((best, current) => 
      current.qualityScore > best.qualityScore ? current : best
    );
    
    this.logger.info(`  Total Datasets: ${analyses.length}`);
    this.logger.info(`  Average Quality: ${avgQuality.toFixed(1)}/100`);
    this.logger.info(`  High Quality (>80): ${highQuality.length}`);
    this.logger.info(`  Best Dataset: ${bestDataset.name} (${bestDataset.qualityScore}/100)`);
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const analyzer = new DataAnalyzer();
  
  if (args.length === 0) {
    await analyzer.analyzeAllDatasets();
  } else {
    const datasetPath = path.join('./data/datasets', args[0]);
    const analysis = await analyzer.analyzeDataset(datasetPath);
    analyzer['logAnalysis'](analysis);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { DataAnalyzer };
