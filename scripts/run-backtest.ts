#!/usr/bin/env node

/**
 * Backtest runner script
 * Usage: npm run backtest [strategy] [dataset]
 */

import { Ultimate8xStrategy } from '../src/strategies/Ultimate8xStrategy.js';
import { Ultimate20xStrategy } from '../src/strategies/Ultimate20xStrategy.js';
import { AdaptiveStrategy } from '../src/strategies/AdaptiveStrategy.js';
import { Config } from '../src/utils/config.js';
import { Logger } from '../src/utils/logger.js';
import { Helpers } from '../src/utils/helpers.js';
import * as fs from 'fs';
import * as path from 'path';

interface BacktestOptions {
  strategy: string;
  dataset?: string;
  initialBalance: number;
  outputFile?: string;
}

class BacktestRunner {
  private config = Config.getInstance();
  private logger = Logger.getInstance();

  async run(options: BacktestOptions): Promise<void> {
    this.logger.info('🚀 Starting Backtest Runner');
    this.logger.info(`Strategy: ${options.strategy}`);
    this.logger.info(`Initial Balance: ${options.initialBalance} USDT`);

    try {
      const datasets = this.getDatasets(options.dataset);
      const results = [];

      for (const dataset of datasets) {
        this.logger.info(`\n📊 Testing on dataset: ${dataset}`);
        
        const result = await this.runBacktest(options.strategy, dataset, options.initialBalance);
        results.push(result);
        
        this.logResult(result);
      }

      // Save combined results
      if (options.outputFile) {
        const outputPath = path.join('./results/backtests', options.outputFile);
        Helpers.saveJSON(results, outputPath);
        this.logger.info(`\n💾 Results saved to: ${outputPath}`);
      }

      // Summary
      this.logSummary(results);

    } catch (error) {
      this.logger.error('Backtest failed', error);
      process.exit(1);
    }
  }

  private getDatasets(specificDataset?: string): string[] {
    const dataDir = './data/datasets';
    
    if (specificDataset) {
      const fullPath = path.join(dataDir, specificDataset);
      if (!fs.existsSync(fullPath)) {
        throw new Error(`Dataset not found: ${specificDataset}`);
      }
      return [specificDataset];
    }

    // Get all CSV files
    const files = fs.readdirSync(dataDir)
      .filter(file => file.endsWith('.csv'))
      .sort();

    if (files.length === 0) {
      throw new Error('No datasets found in data/datasets directory');
    }

    return files;
  }

  private async runBacktest(strategyName: string, dataset: string, initialBalance: number): Promise<any> {
    const dataPath = path.join('./data/datasets', dataset);
    
    let strategy: any;
    
    switch (strategyName.toLowerCase()) {
      case '8x':
      case 'ultimate8x':
        strategy = new Ultimate8xStrategy(initialBalance);
        break;
      case '20x':
      case 'ultimate20x':
        strategy = new Ultimate20xStrategy(initialBalance);
        break;
      case 'adaptive':
        strategy = new AdaptiveStrategy(initialBalance);
        break;
      default:
        throw new Error(`Unknown strategy: ${strategyName}`);
    }

    // Load data and run backtest
    await strategy.loadData(dataPath);
    return await strategy.runBacktest();
  }

  private logResult(result: any): void {
    this.logger.info(`\n📈 Results:`);
    this.logger.info(`  Final Balance: ${result.finalBalance.toFixed(2)} USDT`);
    this.logger.info(`  Total Return: ${result.returnPercent.toFixed(2)}%`);
    this.logger.info(`  Return Multiplier: ${result.returnMultiplier.toFixed(2)}x`);
    this.logger.info(`  Total Trades: ${result.totalTrades}`);
    this.logger.info(`  Win Rate: ${result.winRate.toFixed(2)}%`);
    this.logger.info(`  Max Drawdown: ${result.maxDrawdown.toFixed(2)}%`);
    this.logger.info(`  Target Achieved: ${result.targetAchieved ? '✅' : '❌'}`);
  }

  private logSummary(results: any[]): void {
    if (results.length === 0) return;

    this.logger.info(`\n🎯 BACKTEST SUMMARY`);
    this.logger.info(`${'='.repeat(50)}`);
    
    const successful = results.filter(r => r.targetAchieved);
    const avgReturn = results.reduce((sum, r) => sum + r.returnMultiplier, 0) / results.length;
    const avgWinRate = results.reduce((sum, r) => sum + r.winRate, 0) / results.length;
    const avgDrawdown = results.reduce((sum, r) => sum + r.maxDrawdown, 0) / results.length;

    this.logger.info(`  Total Datasets: ${results.length}`);
    this.logger.info(`  Successful: ${successful.length} (${(successful.length / results.length * 100).toFixed(1)}%)`);
    this.logger.info(`  Average Return: ${avgReturn.toFixed(2)}x`);
    this.logger.info(`  Average Win Rate: ${avgWinRate.toFixed(2)}%`);
    this.logger.info(`  Average Max Drawdown: ${avgDrawdown.toFixed(2)}%`);

    if (successful.length > 0) {
      this.logger.info(`\n🎉 Strategy shows promise with ${successful.length}/${results.length} successful backtests!`);
    } else {
      this.logger.info(`\n⚠️  Strategy needs optimization - no successful backtests achieved target.`);
    }
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  
  const options: BacktestOptions = {
    strategy: args[0] || '8x',
    dataset: args[1],
    initialBalance: parseFloat(args[2]) || 100,
    outputFile: args[3] || `backtest_${Date.now()}.json`
  };

  if (!options.strategy) {
    console.log(`
🚀 Backtest Runner

Usage: npm run backtest [strategy] [dataset] [initialBalance] [outputFile]

Strategies:
  8x, ultimate8x     - Ultimate 8X Strategy
  20x, ultimate20x   - Ultimate 20X Strategy  
  adaptive           - Adaptive Strategy

Examples:
  npm run backtest 8x
  npm run backtest 20x btc_dataset.csv
  npm run backtest adaptive btc_dataset.csv 100 my_results.json

Available datasets:
${fs.readdirSync('./data/datasets').filter(f => f.endsWith('.csv')).map(f => `  - ${f}`).join('\n')}
    `);
    process.exit(0);
  }

  const runner = new BacktestRunner();
  await runner.run(options);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { BacktestRunner };
