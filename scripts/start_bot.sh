#!/bin/bash

# Ultimate 8X Trading Bot Startup Script

echo "🚀 Ultimate 8X Trading Bot Startup"
echo "=================================="

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found!"
    echo "Please create a .env file with your Binance API credentials."
    echo "See REAL_TIME_BOT_README.md for setup instructions."
    exit 1
fi

# Check if API keys are configured based on network mode
if grep -q "USE_TESTNET=true" .env; then
    if grep -q "your_testnet_api_key_here" .env || grep -q "your_binance_api_key_here" .env; then
        echo "❌ Error: Please update your TESTNET API keys in the .env file!"
        echo "Set TESTNET_API_KEY and TESTNET_API_SECRET with your Binance testnet credentials."
        echo "Get them from: https://testnet.binancefuture.com/"
        exit 1
    fi
else
    if grep -q "your_mainnet_api_key_here" .env || grep -q "your_binance_api_key_here" .env; then
        echo "❌ Error: Please update your MAINNET API keys in the .env file!"
        echo "Set MAINNET_API_KEY and MAINNET_API_SECRET with your real Binance credentials."
        echo "Get them from: https://www.binance.com/en/my/settings/api-management"
        exit 1
    fi
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Check network mode
if grep -q "USE_TESTNET=true" .env; then
    echo "🧪 TESTNET MODE: Using Binance testnet (safe)"
    echo "No real money will be used even if trading is enabled."
else
    echo "🔴 MAINNET MODE: Using real Binance network"
    echo "⚠️  WARNING: This will use real money if trading is enabled!"
fi

# Check trading mode
if grep -q "ENABLE_REAL_TRADING=true" .env; then
    echo "⚠️  WARNING: REAL TRADING MODE ENABLED!"
    echo "The bot will place actual trades."
    if grep -q "USE_TESTNET=false" .env; then
        echo "💰 REAL MONEY WILL BE USED!"
    fi
    echo "Make sure you have tested in paper trading mode first."
    echo ""
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Cancelled by user"
        exit 1
    fi
else
    echo "📝 Paper trading mode enabled (safe mode)"
    echo "No real trades will be placed."
fi

echo ""
echo "🎯 Starting Ultimate 8X Trading Bot..."
echo "📊 Web dashboard will be available at: http://localhost:3000"
echo "🛑 Press Ctrl+C to stop the bot safely"
echo ""

# Start the bot
npm run bot
