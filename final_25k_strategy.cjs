const fs = require('fs');
const csv = require('csv-parser');
const { RSI, EMA, SMA } = require('technicalindicators');

class Final25kStrategy {
  constructor(initialBalance = 40) {
    this.data = [];
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "", stopLoss: 0, takeProfit: 0 };
    this.trades = [];
    this.cycleStartTime = "";
    this.totalWithdrawn = 0;
    this.maxBalance = initialBalance;
    this.maxDrawdown = 0;

    // FINAL OPTIMIZED PARAMETERS BASED ON SUCCESSFUL PATTERNS
    this.TARGET_TOTAL = 25000;
    this.KEEP_BALANCE = 40;
    this.CYCLE_DURATION_DAYS = 14; // More frequent withdrawals
    
    // Conservative base with compound scaling
    this.baseLeverage = 25;
    this.basePositionSize = 0.5; // 50% like successful strategy
    this.baseStopLoss = 0.015; // 1.5% like successful strategy
    this.baseTakeProfit = 0.045; // 4.5% like successful strategy
    
    // Current parameters
    this.currentLeverage = this.baseLeverage;
    this.currentPositionSize = this.basePositionSize;
    this.currentStopLoss = this.baseStopLoss;
    this.currentTakeProfit = this.baseTakeProfit;
    
    // RSI levels from successful strategy
    this.rsiOversold = 25;
    this.rsiOverbought = 75;
    this.minVolumeRatio = 1.8;
    this.momentumThreshold = 0.008;

    // Performance tracking
    this.consecutiveLosses = 0;
    this.consecutiveWins = 0;
    this.totalTrades = 0;
    this.compoundingPhase = 1; // Track compounding phases

    console.log("🎯 FINAL 25K STRATEGY - COMPOUND SCALING APPROACH");
    console.log(`💰 Initial Balance: ${initialBalance} USDT`);
    console.log(`🎯 Target Total: ${this.TARGET_TOTAL} USDT`);
    console.log(`📊 Position Size: 50% (compound scaling)`);
    console.log(`⚡ Leverage: 25x (progressive)`);
    console.log(`🎯 Take Profit: 4.5% | Stop Loss: 1.5%`);
    console.log(`⏰ Bi-weekly Profit Withdrawal & Compounding`);
  }

  async loadData(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on('end', () => {
          this.data = results;
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          if (results.length > 0) {
            this.cycleStartTime = results[0].time;
          }
          resolve();
        })
        .on('error', reject);
    });
  }

  shouldWithdrawProfits(currentTime) {
    const currentDate = new Date(currentTime);
    const cycleStartDate = new Date(this.cycleStartTime);
    const daysDiff = (currentDate.getTime() - cycleStartDate.getTime()) / (1000 * 60 * 60 * 24);
    return daysDiff >= this.CYCLE_DURATION_DAYS && this.balance > this.KEEP_BALANCE;
  }

  withdrawProfits(currentTime) {
    if (this.balance <= this.KEEP_BALANCE) return 0;
    
    const withdrawAmount = this.balance - this.KEEP_BALANCE;
    this.totalWithdrawn += withdrawAmount;
    this.balance = this.KEEP_BALANCE;
    
    console.log(`💸 PROFIT WITHDRAWAL at ${currentTime}`);
    console.log(`   Withdrawn: ${withdrawAmount.toFixed(2)} USDT`);
    console.log(`   Total Withdrawn: ${this.totalWithdrawn.toFixed(2)} USDT`);
    console.log(`   Total Value: ${(this.balance + this.totalWithdrawn).toFixed(2)} USDT`);
    console.log(`   Progress: ${((this.balance + this.totalWithdrawn) / this.TARGET_TOTAL * 100).toFixed(2)}%`);
    
    this.cycleStartTime = currentTime;
    this.compoundingPhase++;
    this.adaptParameters();
    return withdrawAmount;
  }

  adaptParameters() {
    const totalValue = this.balance + this.totalWithdrawn;
    const progressRatio = totalValue / this.TARGET_TOTAL;
    
    // Compound scaling approach - increase aggression with each successful cycle
    const phaseMultiplier = Math.min(3, 1 + (this.compoundingPhase - 1) * 0.2);
    
    // Progressive scaling based on success
    if (this.compoundingPhase >= 5) {
      // Advanced phase - more aggressive
      this.currentLeverage = Math.min(50, this.baseLeverage * 1.5);
      this.currentPositionSize = Math.min(0.8, this.basePositionSize * 1.3);
      this.currentTakeProfit = this.baseTakeProfit * 1.5; // 6.75%
      this.currentStopLoss = this.baseStopLoss * 1.2; // 1.8%
    } else if (this.compoundingPhase >= 3) {
      // Intermediate phase
      this.currentLeverage = Math.min(40, this.baseLeverage * 1.3);
      this.currentPositionSize = Math.min(0.7, this.basePositionSize * 1.2);
      this.currentTakeProfit = this.baseTakeProfit * 1.3; // 5.85%
      this.currentStopLoss = this.baseStopLoss * 1.1; // 1.65%
    } else {
      // Early phase - conservative
      this.currentLeverage = this.baseLeverage;
      this.currentPositionSize = this.basePositionSize;
      this.currentTakeProfit = this.baseTakeProfit;
      this.currentStopLoss = this.baseStopLoss;
    }
    
    console.log(`🧠 COMPOUND PHASE ${this.compoundingPhase} (Progress: ${(progressRatio * 100).toFixed(2)}%):`);
    console.log(`   → Leverage: ${this.currentLeverage.toFixed(0)}x`);
    console.log(`   → Position Size: ${(this.currentPositionSize * 100).toFixed(0)}%`);
    console.log(`   → Stop Loss: ${(this.currentStopLoss * 100).toFixed(1)}%`);
    console.log(`   → Take Profit: ${(this.currentTakeProfit * 100).toFixed(1)}%`);
  }

  calculateIndicators(index) {
    const lookback = Math.min(100, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);

    const closes = candles.map(c => c.close);
    const volumes = candles.map(c => c.volume);

    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 12, values: closes });
    const emaSlowValues = EMA.calculate({ period: 26, values: closes });
    const smaValues = SMA.calculate({ period: 50, values: closes });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast: emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      sma: smaValues[smaValues.length - 1] || closes[closes.length - 1],
      volume: volumes[volumes.length - 1],
      avgVolume: volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length
    };
  }

  shouldEnterLong(candle, indicators, prevCandle, index) {
    // Use exact patterns from successful Conservative 20X strategy
    const oversold = indicators.rsi < this.rsiOversold;
    const emaGoldenCross = indicators.emaFast > indicators.emaSlow * 1.002;
    const aboveSMA = candle.close > indicators.sma;
    const greenCandle = candle.close > candle.open;
    const avgVolume = this.data.slice(Math.max(0, index - 10), index).reduce((sum, c) => sum + c.volume, 0) / 10;
    const goodVolume = candle.volume > avgVolume * this.minVolumeRatio;
    const momentum = Math.abs((candle.close - prevCandle.close) / prevCandle.close) > this.momentumThreshold;

    // Exact successful patterns
    if (oversold && emaGoldenCross && aboveSMA && greenCandle && goodVolume) {
      return { enter: true, reason: "OVERSOLD_GOLDEN_CROSS_MOMENTUM", confidence: 0.9 };
    }
    
    if (emaGoldenCross && momentum && goodVolume && greenCandle) {
      return { enter: true, reason: "EMA_CROSS_MOMENTUM", confidence: 0.8 };
    }

    return { enter: false, reason: "NO_SIGNAL", confidence: 0 };
  }

  shouldEnterShort(candle, indicators, prevCandle, index) {
    // Use exact patterns from successful Conservative 20X strategy
    const overbought = indicators.rsi > this.rsiOverbought;
    const emaDeathCross = indicators.emaFast < indicators.emaSlow * 0.998;
    const belowSMA = candle.close < indicators.sma;
    const redCandle = candle.close < candle.open;
    const avgVolume = this.data.slice(Math.max(0, index - 10), index).reduce((sum, c) => sum + c.volume, 0) / 10;
    const goodVolume = candle.volume > avgVolume * this.minVolumeRatio;
    const momentum = Math.abs((candle.close - prevCandle.close) / prevCandle.close) > this.momentumThreshold;

    // Exact successful patterns
    if (overbought && emaDeathCross && belowSMA && redCandle && goodVolume) {
      return { enter: true, reason: "OVERBOUGHT_DEATH_CROSS_MOMENTUM", confidence: 0.9 };
    }
    
    if (emaDeathCross && momentum && goodVolume && redCandle) {
      return { enter: true, reason: "EMA_DEATH_MOMENTUM", confidence: 0.8 };
    }

    return { enter: false, reason: "NO_SIGNAL", confidence: 0 };
  }

  shouldExit(candle, indicators) {
    if (!this.position.side) return { exit: false, reason: "NO_POSITION" };

    const currentPrice = candle.close;
    
    if (this.position.side === "LONG") {
      if (currentPrice <= this.position.stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice >= this.position.takeProfit) return { exit: true, reason: "TAKE_PROFIT" };
      if (indicators.rsi > this.rsiOverbought) return { exit: true, reason: "RSI_OVERBOUGHT" };
    } else {
      if (currentPrice >= this.position.stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice <= this.position.takeProfit) return { exit: true, reason: "TAKE_PROFIT" };
      if (indicators.rsi < this.rsiOversold) return { exit: true, reason: "RSI_OVERSOLD" };
    }

    return { exit: false, reason: "HOLD" };
  }

  openPosition(side, candle, reason, confidence) {
    const positionSize = this.balance * this.currentPositionSize;
    const leverage = this.currentLeverage;
    const contractSize = (positionSize * leverage) / candle.close;

    let stopLoss, takeProfit;
    
    if (side === "LONG") {
      stopLoss = candle.close * (1 - this.currentStopLoss);
      takeProfit = candle.close * (1 + this.currentTakeProfit);
    } else {
      stopLoss = candle.close * (1 + this.currentStopLoss);
      takeProfit = candle.close * (1 - this.currentTakeProfit);
    }

    this.position = {
      side,
      size: contractSize,
      entryPrice: candle.close,
      entryTime: candle.time,
      leverage,
      entryReason: reason,
      stopLoss,
      takeProfit
    };

    console.log(`🟢 OPEN ${side} at ${candle.close.toFixed(2)} | Size: ${positionSize.toFixed(2)} USDT (${leverage}x)`);
    console.log(`   SL: ${stopLoss.toFixed(2)} | TP: ${takeProfit.toFixed(2)} | Balance: ${this.balance.toFixed(2)}`);
    console.log(`   Reason: ${reason} | Phase: ${this.compoundingPhase}`);
  }

  closePosition(candle, reason) {
    if (!this.position.side) return;

    const exitPrice = candle.close;
    const entryPrice = this.position.entryPrice;
    const size = this.position.size;

    let pnl = 0;
    if (this.position.side === "LONG") {
      pnl = size * (exitPrice - entryPrice);
    } else {
      pnl = size * (entryPrice - exitPrice);
    }

    const pnlPercent = (pnl / (this.balance * this.currentPositionSize)) * 100;
    const balanceBefore = this.balance;
    this.balance += pnl;

    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }
    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    if (pnl > 0) {
      this.consecutiveWins++;
      this.consecutiveLosses = 0;
    } else {
      this.consecutiveLosses++;
      this.consecutiveWins = 0;
    }

    const trade = {
      side: this.position.side,
      entryPrice,
      exitPrice,
      entryTime: this.position.entryTime,
      exitTime: candle.time,
      size,
      pnl,
      pnlPercent,
      reason,
      entryReason: this.position.entryReason,
      leverage: this.position.leverage,
      balanceBefore,
      balanceAfter: this.balance,
      compoundingPhase: this.compoundingPhase
    };

    this.trades.push(trade);
    this.totalTrades++;

    const pnlColor = pnl > 0 ? "🟢" : "🔴";
    const totalValue = this.balance + this.totalWithdrawn;
    const progress = (totalValue / this.TARGET_TOTAL * 100).toFixed(2);

    console.log(`${pnlColor} CLOSE ${this.position.side} | PnL: ${pnl.toFixed(2)} (${pnlPercent.toFixed(2)}%)`);
    console.log(`   Balance: ${this.balance.toFixed(2)} | Total Value: ${totalValue.toFixed(2)} | Progress: ${progress}%`);
    console.log(`   ${reason} | Trade #${this.totalTrades} | Phase: ${this.compoundingPhase}`);

    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "", stopLoss: 0, takeProfit: 0 };
  }

  async runBacktest() {
    console.log(`\n🚀 Starting Final 25K Strategy Backtest`);
    console.log(`📊 Data points: ${this.data.length}`);
    console.log(`⏰ Period: ${this.data[0]?.time} → ${this.data[this.data.length - 1]?.time}`);
    console.log(`🎯 Target: ${this.TARGET_TOTAL} USDT (${(this.TARGET_TOTAL / this.initialBalance).toFixed(0)}x return)`);

    for (let i = 50; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      // Check for profit withdrawal
      if (this.shouldWithdrawProfits(candle.time)) {
        this.withdrawProfits(candle.time);
      }

      // Exit logic
      if (this.position.side) {
        const exitSignal = this.shouldExit(candle, indicators);
        if (exitSignal.exit) {
          this.closePosition(candle, exitSignal.reason);
        }
      }

      // Entry logic (only if no position and sufficient balance)
      if (!this.position.side && this.balance >= 10) {
        const longSignal = this.shouldEnterLong(candle, indicators, prevCandle, i);
        const shortSignal = this.shouldEnterShort(candle, indicators, prevCandle, i);

        // Enter trades with high confidence
        if (longSignal.enter && longSignal.confidence >= 0.8) {
          this.openPosition("LONG", candle, longSignal.reason, longSignal.confidence);
        } else if (shortSignal.enter && shortSignal.confidence >= 0.8) {
          this.openPosition("SHORT", candle, shortSignal.reason, shortSignal.confidence);
        }
      }

      // Progress updates every 2000 candles
      if (i % 2000 === 0) {
        const totalValue = this.balance + this.totalWithdrawn;
        const progress = (totalValue / this.TARGET_TOTAL * 100).toFixed(2);
        console.log(`📊 Progress Update: ${totalValue.toFixed(2)} USDT (${progress}%) | Trades: ${this.trades.length} | Phase: ${this.compoundingPhase}`);
      }

      // Early exit if target achieved
      const currentTotalValue = this.balance + this.totalWithdrawn;
      if (currentTotalValue >= this.TARGET_TOTAL) {
        console.log(`🎉 TARGET ACHIEVED! Stopping backtest early.`);
        break;
      }
    }

    // Close any remaining position
    if (this.position.side) {
      this.closePosition(this.data[this.data.length - 1], "END_OF_DATA");
    }

    this.printResults();
  }

  printResults() {
    const finalBalance = this.balance;
    const totalValue = finalBalance + this.totalWithdrawn;
    const totalReturn = totalValue - this.initialBalance;
    const returnPercent = (totalReturn / this.initialBalance) * 100;
    const returnMultiplier = totalValue / this.initialBalance;

    const winningTrades = this.trades.filter(t => t.pnl > 0);
    const losingTrades = this.trades.filter(t => t.pnl <= 0);
    const winRate = this.trades.length > 0 ? (winningTrades.length / this.trades.length) * 100 : 0;
    const avgWin = winningTrades.length > 0 ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length : 0;
    const profitFactor = Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : avgWin > 0 ? 10 : 0;

    console.log("\n" + "=".repeat(80));
    console.log("🎯 FINAL 25K STRATEGY - COMPOUND SCALING RESULTS");
    console.log("=".repeat(80));
    console.log(`💰 Initial Balance: ${this.initialBalance.toFixed(2)} USDT`);
    console.log(`💰 Final Balance: ${finalBalance.toFixed(2)} USDT`);
    console.log(`💸 Total Withdrawn: ${this.totalWithdrawn.toFixed(2)} USDT`);
    console.log(`💎 Total Value: ${totalValue.toFixed(2)} USDT`);
    console.log(`📈 Total Return: ${totalReturn.toFixed(2)} USDT (${returnPercent.toFixed(2)}%)`);
    console.log(`🚀 Return Multiplier: ${returnMultiplier.toFixed(2)}x`);
    console.log(`🎯 Target Progress: ${(totalValue / this.TARGET_TOTAL * 100).toFixed(2)}% (${this.TARGET_TOTAL.toFixed(0)} USDT target)`);
    console.log(`🏆 Target Achieved: ${totalValue >= this.TARGET_TOTAL ? '✅ YES!' : '❌ NO'}`);
    console.log(`📊 Total Trades: ${this.trades.length}`);
    console.log(`🏆 Win Rate: ${winRate.toFixed(2)}%`);
    console.log(`📉 Max Drawdown: ${(this.maxDrawdown * 100).toFixed(2)}%`);
    console.log(`💎 Profit Factor: ${profitFactor.toFixed(2)}`);
    console.log(`🔄 Compounding Phases Reached: ${this.compoundingPhase}`);

    if (totalValue >= this.TARGET_TOTAL) {
      console.log("\n🎉 CONGRATULATIONS! 25,000 USDT TARGET ACHIEVED!");
      console.log(`🚀 Your ${this.initialBalance} USDT became ${totalValue.toFixed(2)} USDT!`);
      console.log(`💰 That's a ${returnMultiplier.toFixed(0)}x return!`);
    } else {
      console.log(`\n📈 Progress: ${(totalValue / this.TARGET_TOTAL * 100).toFixed(1)}% towards 25K target`);
      console.log(`💡 Strategy shows ${returnMultiplier.toFixed(2)}x return potential`);
      console.log(`🎯 Need ${(this.TARGET_TOTAL - totalValue).toFixed(2)} more USDT to reach target`);

      // Calculate what return multiplier would be needed
      const neededMultiplier = this.TARGET_TOTAL / this.initialBalance;
      const currentMultiplier = returnMultiplier;
      const additionalMultiplierNeeded = neededMultiplier / currentMultiplier;

      console.log(`📊 Analysis: Need ${neededMultiplier.toFixed(0)}x total return`);
      console.log(`📊 Current: ${currentMultiplier.toFixed(2)}x achieved`);
      console.log(`📊 Additional: ${additionalMultiplierNeeded.toFixed(1)}x more needed`);
    }

    // Save results
    const result = {
      strategy: "Final 25K Strategy - Compound Scaling",
      initialBalance: this.initialBalance,
      finalBalance,
      totalWithdrawn: this.totalWithdrawn,
      totalValue,
      totalReturn,
      returnPercent,
      returnMultiplier,
      maxBalance: this.maxBalance,
      maxDrawdown: this.maxDrawdown * 100,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,
      avgWin,
      avgLoss,
      profitFactor,
      targetAchieved: totalValue >= this.TARGET_TOTAL,
      targetProgress: (totalValue / this.TARGET_TOTAL) * 100,
      compoundingPhases: this.compoundingPhase,
      trades: this.trades,
      dataStart: this.data[0]?.time,
      dataEnd: this.data[this.data.length - 1]?.time,
    };

    const resultsFile = `./results/backtests/final_25k_${Date.now()}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to: ${resultsFile}`);
  }
}

// Run the strategy
async function main() {
  const strategy = new Final25kStrategy(40);

  try {
    const dataFile = './data/history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv';
    console.log(`🚀 Running Final 25K Strategy on: ${dataFile}`);

    await strategy.loadData(dataFile);
    await strategy.runBacktest();
  } catch (error) {
    console.error('❌ Error running backtest:', error);
  }
}

main().catch(console.error);
