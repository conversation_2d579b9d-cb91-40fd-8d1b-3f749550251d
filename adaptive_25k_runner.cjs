const fs = require('fs');
const csv = require('csv-parser');
const { RSI, EMA, SMA, MACD, BollingerBands, ATR } = require('technicalindicators');

class Adaptive25kStrategy {
  constructor(initialBalance = 40) {
    this.data = [];
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "", confidence: 0, stopLoss: 0, takeProfit: 0 };
    this.trades = [];
    this.cycles = [];
    this.currentCycle = 1;
    this.cycleStartTime = "";
    this.cycleStartBalance = initialBalance;
    this.totalWithdrawn = 0;
    this.maxBalance = initialBalance;
    this.maxDrawdown = 0;

    // Adaptive parameters
    this.BASE_LEVERAGE = 30;
    this.BASE_POSITION_SIZE = 0.5;
    this.KEEP_BALANCE = 40;
    this.CYCLE_DURATION_DAYS = 30;
    this.TARGET_TOTAL = 25000;

    // Dynamic parameters
    this.currentLeverage = 30;
    this.currentPositionSize = 0.5;
    this.currentStopLoss = 0.02;
    this.currentTakeProfit = 0.06;
    this.rsiOversold = 20;
    this.rsiOverbought = 80;
    this.minVolumeRatio = 1.5;
    this.momentumThreshold = 0.01;

    // Market regime
    this.currentRegime = { type: "CHOPPY", volatility: 0, volume: 0, momentum: 0, confidence: 0 };

    // Performance tracking
    this.recentWinRate = 0;
    this.recentProfitFactor = 0;
    this.consecutiveLosses = 0;
    this.consecutiveWins = 0;

    console.log("🎯 ADAPTIVE 25K STRATEGY - TARGET: 625x RETURN");
    console.log(`💰 Initial Balance: ${initialBalance} USDT`);
    console.log(`🎯 Target Total: ${this.TARGET_TOTAL} USDT`);
    console.log(`📊 Adaptive Position Size: 50% base (dynamic)`);
    console.log(`⏰ Monthly Cycles with Profit Withdrawal`);
    console.log(`🧠 AI-Powered Market Regime Detection`);
  }

  async loadData(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on('end', () => {
          this.data = results;
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          if (results.length > 0) {
            this.cycleStartTime = results[0].time;
          }
          resolve();
        })
        .on('error', reject);
    });
  }

  shouldWithdrawProfits(currentTime) {
    const currentDate = new Date(currentTime);
    const cycleStartDate = new Date(this.cycleStartTime);
    const daysDiff = (currentDate.getTime() - cycleStartDate.getTime()) / (1000 * 60 * 60 * 24);
    return daysDiff >= this.CYCLE_DURATION_DAYS && this.balance > this.KEEP_BALANCE;
  }

  withdrawProfits(currentTime) {
    if (this.balance <= this.KEEP_BALANCE) return 0;

    const withdrawAmount = this.balance - this.KEEP_BALANCE;
    this.totalWithdrawn += withdrawAmount;
    this.balance = this.KEEP_BALANCE;

    console.log(`💸 PROFIT WITHDRAWAL at ${currentTime}`);
    console.log(`   Withdrawn: ${withdrawAmount.toFixed(2)} USDT`);
    console.log(`   Total Withdrawn: ${this.totalWithdrawn.toFixed(2)} USDT`);
    console.log(`   Total Value: ${(this.balance + this.totalWithdrawn).toFixed(2)} USDT`);
    console.log(`   Progress: ${((this.balance + this.totalWithdrawn) / this.TARGET_TOTAL * 100).toFixed(2)}%`);

    this.cycleStartTime = currentTime;
    this.adaptParameters();
    return withdrawAmount;
  }

  calculateMarketRegime(index) {
    const lookback = Math.min(50, index);
    const recentCandles = this.data.slice(Math.max(0, index - lookback), index + 1);

    const closes = recentCandles.map(c => c.close);
    const highs = recentCandles.map(c => c.high);
    const lows = recentCandles.map(c => c.low);

    const atrValues = ATR.calculate({
      high: highs,
      low: lows,
      close: closes,
      period: Math.min(14, recentCandles.length - 1)
    });

    const currentATR = atrValues[atrValues.length - 1] || 0;
    const avgPrice = closes[closes.length - 1];
    const volatility = avgPrice > 0 ? currentATR / avgPrice : 0;

    const volumes = recentCandles.map(c => c.volume);
    const avgVolume = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
    const currentVolume = volumes[volumes.length - 1];
    const volumeRatio = avgVolume > 0 ? currentVolume / avgVolume : 1;

    const priceChange = closes.length >= 10 ?
      (closes[closes.length - 1] - closes[closes.length - 10]) / closes[closes.length - 10] : 0;
    const momentum = Math.abs(priceChange);

    let regimeType, confidence;

    if (volatility > 0.04 && volumeRatio > 3 && momentum > 0.02) {
      regimeType = "EXPLOSIVE";
      confidence = 0.9;
    } else if (volatility > 0.025 && volumeRatio > 1.8 && momentum > 0.01) {
      regimeType = "TRENDING";
      confidence = 0.8;
    } else if (volatility > 0.015) {
      regimeType = "CHOPPY";
      confidence = 0.6;
    } else {
      regimeType = "DEAD";
      confidence = 0.4;
    }

    return { type: regimeType, volatility, volume: volumeRatio, momentum, confidence };
  }

  adaptParameters() {
    const recentTrades = this.trades.slice(-10);
    const winningTrades = recentTrades.filter(t => t.pnl > 0);
    this.recentWinRate = recentTrades.length > 0 ? winningTrades.length / recentTrades.length : 0;

    const totalWins = winningTrades.reduce((sum, t) => sum + t.pnl, 0);
    const totalLosses = Math.abs(recentTrades.filter(t => t.pnl < 0).reduce((sum, t) => sum + t.pnl, 0));
    this.recentProfitFactor = totalLosses > 0 ? totalWins / totalLosses : totalWins > 0 ? 5 : 0;

    const totalValue = this.balance + this.totalWithdrawn;
    const progressRatio = totalValue / this.TARGET_TOTAL;

    console.log(`🧠 ADAPTING PARAMETERS:`);
    console.log(`   Recent Win Rate: ${(this.recentWinRate * 100).toFixed(1)}%`);
    console.log(`   Recent Profit Factor: ${this.recentProfitFactor.toFixed(2)}`);
    console.log(`   Market Regime: ${this.currentRegime.type}`);
    console.log(`   Progress: ${(progressRatio * 100).toFixed(2)}%`);

    // Adaptive leverage
    if (this.currentRegime.type === "EXPLOSIVE" && this.recentWinRate > 0.6) {
      this.currentLeverage = Math.min(60, this.BASE_LEVERAGE + 30);
    } else if (this.currentRegime.type === "TRENDING" && this.recentWinRate > 0.5) {
      this.currentLeverage = this.BASE_LEVERAGE + 15;
    } else if (this.recentWinRate < 0.4) {
      this.currentLeverage = Math.max(15, this.BASE_LEVERAGE - 15);
    } else {
      this.currentLeverage = this.BASE_LEVERAGE;
    }

    // Adaptive position sizing
    if (this.recentProfitFactor > 2 && progressRatio < 0.5) {
      this.currentPositionSize = Math.min(0.8, this.BASE_POSITION_SIZE + 0.3);
    } else if (this.recentProfitFactor < 0.8) {
      this.currentPositionSize = Math.max(0.3, this.BASE_POSITION_SIZE - 0.2);
    } else {
      this.currentPositionSize = this.BASE_POSITION_SIZE;
    }

    // Adaptive stop loss and take profit
    if (this.currentRegime.type === "EXPLOSIVE") {
      this.currentStopLoss = 0.03;
      this.currentTakeProfit = 0.10;
    } else if (this.currentRegime.type === "TRENDING") {
      this.currentStopLoss = 0.025;
      this.currentTakeProfit = 0.08;
    } else if (this.currentRegime.type === "CHOPPY") {
      this.currentStopLoss = 0.02;
      this.currentTakeProfit = 0.06;
    } else {
      this.currentStopLoss = 0.015;
      this.currentTakeProfit = 0.04;
    }

    // Adaptive RSI levels
    if (this.currentRegime.type === "EXPLOSIVE") {
      this.rsiOversold = 10;
      this.rsiOverbought = 90;
    } else if (this.currentRegime.type === "TRENDING") {
      this.rsiOversold = 15;
      this.rsiOverbought = 85;
    } else {
      this.rsiOversold = 20;
      this.rsiOverbought = 80;
    }

    console.log(`   → Leverage: ${this.currentLeverage}x`);
    console.log(`   → Position Size: ${(this.currentPositionSize * 100).toFixed(0)}%`);
    console.log(`   → Stop Loss: ${(this.currentStopLoss * 100).toFixed(1)}%`);
    console.log(`   → Take Profit: ${(this.currentTakeProfit * 100).toFixed(1)}%`);
  }

  calculatePositionSize() {
    return this.balance * this.currentPositionSize;
  }

  calculateIndicators(index) {
    const lookback = Math.min(100, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);

    const closes = candles.map(c => c.close);
    const volumes = candles.map(c => c.volume);

    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 12, values: closes });
    const emaSlowValues = EMA.calculate({ period: 26, values: closes });
    const smaValues = SMA.calculate({ period: 50, values: closes });
    const macdValues = MACD.calculate({
      values: closes,
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
      SimpleMAOscillator: false,
      SimpleMASignal: false
    });
    const bbValues = BollingerBands.calculate({
      values: closes,
      period: 20,
      stdDev: 2
    });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast: emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      sma: smaValues[smaValues.length - 1] || closes[closes.length - 1],
      macd: macdValues[macdValues.length - 1] || { MACD: 0, signal: 0, histogram: 0 },
      bb: bbValues[bbValues.length - 1] || { upper: closes[closes.length - 1], middle: closes[closes.length - 1], lower: closes[closes.length - 1] },
      volume: volumes[volumes.length - 1],
      avgVolume: volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length
    };
  }

  calculateConfidence(candle, indicators, regime) {
    let confidence = 0;

    confidence += regime.confidence * 0.3;

    const volumeRatio = indicators.avgVolume > 0 ? indicators.volume / indicators.avgVolume : 1;
    if (volumeRatio > this.minVolumeRatio) confidence += 0.2;

    if (indicators.rsi < this.rsiOversold || indicators.rsi > this.rsiOverbought) confidence += 0.2;

    const emaSpread = Math.abs(indicators.emaFast - indicators.emaSlow) / indicators.emaSlow;
    if (emaSpread > 0.005) confidence += 0.15;

    if (indicators.macd.MACD && indicators.macd.signal) {
      if ((indicators.macd.MACD > indicators.macd.signal && indicators.emaFast > indicators.emaSlow) ||
          (indicators.macd.MACD < indicators.macd.signal && indicators.emaFast < indicators.emaSlow)) {
        confidence += 0.15;
      }
    }

    return Math.min(1, confidence);
  }

  shouldEnterLong(candle, indicators, prevCandle, index) {
    const regime = this.calculateMarketRegime(index);
    this.currentRegime = regime;

    const oversold = indicators.rsi < this.rsiOversold;
    const emaGoldenCross = indicators.emaFast > indicators.emaSlow * 1.003;
    const aboveSMA = candle.close > indicators.sma;
    const greenCandle = candle.close > candle.open;
    const volumeRatio = indicators.avgVolume > 0 ? indicators.volume / indicators.avgVolume : 1;
    const goodVolume = volumeRatio > this.minVolumeRatio;
    const momentum = Math.abs((candle.close - prevCandle.close) / prevCandle.close) > this.momentumThreshold;
    const nearBBLower = indicators.bb && candle.close < indicators.bb.middle;

    const confidence = this.calculateConfidence(candle, indicators, regime);

    if (regime.type === "EXPLOSIVE" && confidence > 0.8) {
      if (emaGoldenCross && momentum && goodVolume && greenCandle) {
        return { enter: true, reason: "EXPLOSIVE_MOMENTUM_BREAKOUT", confidence };
      }
    }

    if (regime.type === "TRENDING" && confidence > 0.7) {
      if (oversold && emaGoldenCross && aboveSMA && goodVolume) {
        return { enter: true, reason: "TRENDING_OVERSOLD_REVERSAL", confidence };
      }
      if (emaGoldenCross && momentum && goodVolume && greenCandle) {
        return { enter: true, reason: "TRENDING_EMA_MOMENTUM", confidence };
      }
    }

    if (regime.type === "CHOPPY" && confidence > 0.6) {
      if (oversold && nearBBLower && goodVolume) {
        return { enter: true, reason: "CHOPPY_OVERSOLD_BOUNCE", confidence };
      }
    }

    if (regime.type === "DEAD" && confidence > 0.5) {
      if (oversold && emaGoldenCross && momentum && goodVolume) {
        return { enter: true, reason: "DEAD_RARE_OPPORTUNITY", confidence };
      }
    }

    return { enter: false, reason: "NO_SIGNAL", confidence: 0 };
  }

  shouldEnterShort(candle, indicators, prevCandle, index) {
    const regime = this.calculateMarketRegime(index);
    this.currentRegime = regime;

    const overbought = indicators.rsi > this.rsiOverbought;
    const emaDeathCross = indicators.emaFast < indicators.emaSlow * 0.997;
    const belowSMA = candle.close < indicators.sma;
    const redCandle = candle.close < candle.open;
    const volumeRatio = indicators.avgVolume > 0 ? indicators.volume / indicators.avgVolume : 1;
    const goodVolume = volumeRatio > this.minVolumeRatio;
    const momentum = Math.abs((candle.close - prevCandle.close) / prevCandle.close) > this.momentumThreshold;
    const nearBBUpper = indicators.bb && candle.close > indicators.bb.middle;

    const confidence = this.calculateConfidence(candle, indicators, regime);

    if (regime.type === "EXPLOSIVE" && confidence > 0.8) {
      if (emaDeathCross && momentum && goodVolume && redCandle) {
        return { enter: true, reason: "EXPLOSIVE_MOMENTUM_BREAKDOWN", confidence };
      }
    }

    if (regime.type === "TRENDING" && confidence > 0.7) {
      if (overbought && emaDeathCross && belowSMA && goodVolume) {
        return { enter: true, reason: "TRENDING_OVERBOUGHT_REVERSAL", confidence };
      }
      if (emaDeathCross && momentum && goodVolume && redCandle) {
        return { enter: true, reason: "TRENDING_EMA_MOMENTUM", confidence };
      }
    }

    if (regime.type === "CHOPPY" && confidence > 0.6) {
      if (overbought && nearBBUpper && goodVolume) {
        return { enter: true, reason: "CHOPPY_OVERBOUGHT_REJECTION", confidence };
      }
    }

    if (regime.type === "DEAD" && confidence > 0.5) {
      if (overbought && emaDeathCross && momentum && goodVolume) {
        return { enter: true, reason: "DEAD_RARE_OPPORTUNITY", confidence };
      }
    }

    return { enter: false, reason: "NO_SIGNAL", confidence: 0 };
  }

  shouldExit(candle, indicators) {
    if (!this.position.side) return { exit: false, reason: "NO_POSITION" };

    const currentPrice = candle.close;

    if (this.position.side === "LONG") {
      if (currentPrice <= this.position.stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice >= this.position.takeProfit) return { exit: true, reason: "TAKE_PROFIT" };

      if (this.currentRegime.type === "EXPLOSIVE" && indicators.rsi > 90) {
        return { exit: true, reason: "EXTREME_OVERBOUGHT" };
      }
      if (this.currentRegime.type === "CHOPPY" && indicators.rsi > this.rsiOverbought) {
        return { exit: true, reason: "RSI_OVERBOUGHT" };
      }

      const profitPercent = (currentPrice - this.position.entryPrice) / this.position.entryPrice;
      if (profitPercent > this.currentTakeProfit * 0.5) {
        const trailingStop = this.position.entryPrice * (1 + profitPercent * 0.7);
        if (currentPrice < trailingStop) {
          return { exit: true, reason: "TRAILING_STOP" };
        }
      }

    } else {
      if (currentPrice >= this.position.stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice <= this.position.takeProfit) return { exit: true, reason: "TAKE_PROFIT" };

      if (this.currentRegime.type === "EXPLOSIVE" && indicators.rsi < 10) {
        return { exit: true, reason: "EXTREME_OVERSOLD" };
      }
      if (this.currentRegime.type === "CHOPPY" && indicators.rsi < this.rsiOversold) {
        return { exit: true, reason: "RSI_OVERSOLD" };
      }

      const profitPercent = (this.position.entryPrice - currentPrice) / this.position.entryPrice;
      if (profitPercent > this.currentTakeProfit * 0.5) {
        const trailingStop = this.position.entryPrice * (1 - profitPercent * 0.7);
        if (currentPrice > trailingStop) {
          return { exit: true, reason: "TRAILING_STOP" };
        }
      }
    }

    return { exit: false, reason: "HOLD" };
  }

  openPosition(side, candle, reason, confidence) {
    const positionSize = this.calculatePositionSize();
    const leverage = this.currentLeverage;
    const contractSize = (positionSize * leverage) / candle.close;

    let stopLossPercent = this.currentStopLoss;
    let takeProfitPercent = this.currentTakeProfit;

    if (confidence > 0.8) {
      takeProfitPercent *= 1.3;
      stopLossPercent *= 1.2;
    } else if (confidence < 0.6) {
      takeProfitPercent *= 0.8;
      stopLossPercent *= 0.8;
    }

    let stopLoss, takeProfit;

    if (side === "LONG") {
      stopLoss = candle.close * (1 - stopLossPercent);
      takeProfit = candle.close * (1 + takeProfitPercent);
    } else {
      stopLoss = candle.close * (1 + stopLossPercent);
      takeProfit = candle.close * (1 - takeProfitPercent);
    }

    this.position = {
      side,
      size: contractSize,
      entryPrice: candle.close,
      entryTime: candle.time,
      leverage,
      entryReason: reason,
      confidence,
      stopLoss,
      takeProfit
    };

    console.log(`🟢 OPEN ${side} at ${candle.close.toFixed(2)} | Size: ${positionSize.toFixed(2)} USDT (${leverage}x)`);
    console.log(`   Confidence: ${(confidence * 100).toFixed(1)}% | Regime: ${this.currentRegime.type}`);
    console.log(`   SL: ${stopLoss.toFixed(2)} | TP: ${takeProfit.toFixed(2)} | Balance: ${this.balance.toFixed(2)}`);
    console.log(`   Reason: ${reason}`);
  }

  closePosition(candle, reason) {
    if (!this.position.side) return;

    const exitPrice = candle.close;
    const entryPrice = this.position.entryPrice;
    const size = this.position.size;

    let pnl = 0;
    if (this.position.side === "LONG") {
      pnl = size * (exitPrice - entryPrice);
    } else {
      pnl = size * (entryPrice - exitPrice);
    }

    const pnlPercent = (pnl / (this.balance * this.currentPositionSize)) * 100;
    const balanceBefore = this.balance;
    this.balance += pnl;

    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }
    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    if (pnl > 0) {
      this.consecutiveWins++;
      this.consecutiveLosses = 0;
    } else {
      this.consecutiveLosses++;
      this.consecutiveWins = 0;
    }

    const trade = {
      side: this.position.side,
      entryPrice,
      exitPrice,
      entryTime: this.position.entryTime,
      exitTime: candle.time,
      size,
      pnl,
      pnlPercent,
      reason,
      entryReason: this.position.entryReason,
      leverage: this.position.leverage,
      balanceBefore,
      balanceAfter: this.balance,
      cycle: this.currentCycle,
      confidence: this.position.confidence,
      marketRegime: this.currentRegime.type
    };

    this.trades.push(trade);

    const pnlColor = pnl > 0 ? "🟢" : "🔴";
    const totalValue = this.balance + this.totalWithdrawn;
    const progress = (totalValue / this.TARGET_TOTAL * 100).toFixed(2);

    console.log(`${pnlColor} CLOSE ${this.position.side} | PnL: ${pnl.toFixed(2)} (${pnlPercent.toFixed(2)}%)`);
    console.log(`   Balance: ${this.balance.toFixed(2)} | Total Value: ${totalValue.toFixed(2)} | Progress: ${progress}%`);
    console.log(`   ${reason} | Confidence was: ${(this.position.confidence * 100).toFixed(1)}%`);

    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "", confidence: 0, stopLoss: 0, takeProfit: 0 };
  }

  async runBacktest() {
    console.log(`\n🚀 Starting Adaptive 25K Strategy Backtest`);
    console.log(`📊 Data points: ${this.data.length}`);
    console.log(`⏰ Period: ${this.data[0]?.time} → ${this.data[this.data.length - 1]?.time}`);
    console.log(`🎯 Target: ${this.TARGET_TOTAL} USDT (${(this.TARGET_TOTAL / this.initialBalance).toFixed(0)}x return)`);

    for (let i = 100; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      if (this.shouldWithdrawProfits(candle.time)) {
        this.withdrawProfits(candle.time);
      }

      if (this.position.side) {
        const exitSignal = this.shouldExit(candle, indicators);
        if (exitSignal.exit) {
          this.closePosition(candle, exitSignal.reason);
        }
      }

      if (!this.position.side && this.balance >= 20) {
        const longSignal = this.shouldEnterLong(candle, indicators, prevCandle, i);
        const shortSignal = this.shouldEnterShort(candle, indicators, prevCandle, i);

        if (longSignal.enter && longSignal.confidence > 0.5) {
          this.openPosition("LONG", candle, longSignal.reason, longSignal.confidence);
        } else if (shortSignal.enter && shortSignal.confidence > 0.5) {
          this.openPosition("SHORT", candle, shortSignal.reason, shortSignal.confidence);
        }
      }

      if (this.consecutiveLosses >= 5 && this.position.side) {
        this.closePosition(candle, "EMERGENCY_STOP");
        console.log("⚠️ EMERGENCY STOP: Too many consecutive losses");
      }

      if (i % 1000 === 0) {
        const totalValue = this.balance + this.totalWithdrawn;
        const progress = (totalValue / this.TARGET_TOTAL * 100).toFixed(2);
        console.log(`📊 Progress Update: ${totalValue.toFixed(2)} USDT (${progress}%) | Trades: ${this.trades.length}`);
      }
    }

    if (this.position.side) {
      this.closePosition(this.data[this.data.length - 1], "END_OF_DATA");
    }

    this.printResults();
  }

  printResults() {
    const finalBalance = this.balance;
    const totalValue = finalBalance + this.totalWithdrawn;
    const totalReturn = totalValue - this.initialBalance;
    const returnPercent = (totalReturn / this.initialBalance) * 100;
    const returnMultiplier = totalValue / this.initialBalance;

    const winningTrades = this.trades.filter(t => t.pnl > 0);
    const losingTrades = this.trades.filter(t => t.pnl <= 0);
    const winRate = this.trades.length > 0 ? (winningTrades.length / this.trades.length) * 100 : 0;
    const avgWin = winningTrades.length > 0 ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length : 0;
    const profitFactor = Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : avgWin > 0 ? 10 : 0;

    const regimeStats = {};
    ["EXPLOSIVE", "TRENDING", "CHOPPY", "DEAD"].forEach(regime => {
      const regimeTrades = this.trades.filter(t => t.marketRegime === regime);
      const regimeWins = regimeTrades.filter(t => t.pnl > 0);
      regimeStats[regime] = {
        trades: regimeTrades.length,
        winRate: regimeTrades.length > 0 ? (regimeWins.length / regimeTrades.length) * 100 : 0,
        avgPnL: regimeTrades.length > 0 ? regimeTrades.reduce((sum, t) => sum + t.pnl, 0) / regimeTrades.length : 0
      };
    });

    console.log("\n" + "=".repeat(80));
    console.log("🎯 ADAPTIVE 25K STRATEGY - FINAL RESULTS");
    console.log("=".repeat(80));
    console.log(`💰 Initial Balance: ${this.initialBalance.toFixed(2)} USDT`);
    console.log(`💰 Final Balance: ${finalBalance.toFixed(2)} USDT`);
    console.log(`💸 Total Withdrawn: ${this.totalWithdrawn.toFixed(2)} USDT`);
    console.log(`💎 Total Value: ${totalValue.toFixed(2)} USDT`);
    console.log(`📈 Total Return: ${totalReturn.toFixed(2)} USDT (${returnPercent.toFixed(2)}%)`);
    console.log(`🚀 Return Multiplier: ${returnMultiplier.toFixed(2)}x`);
    console.log(`🎯 Target Progress: ${(totalValue / this.TARGET_TOTAL * 100).toFixed(2)}% (${this.TARGET_TOTAL.toFixed(0)} USDT target)`);
    console.log(`🏆 Target Achieved: ${totalValue >= this.TARGET_TOTAL ? '✅ YES!' : '❌ NO'}`);
    console.log(`📊 Total Trades: ${this.trades.length}`);
    console.log(`🏆 Win Rate: ${winRate.toFixed(2)}%`);
    console.log(`📉 Max Drawdown: ${(this.maxDrawdown * 100).toFixed(2)}%`);
    console.log(`💎 Profit Factor: ${profitFactor.toFixed(2)}`);

    console.log("\n📊 MARKET REGIME PERFORMANCE:");
    Object.entries(regimeStats).forEach(([regime, stats]) => {
      if (stats.trades > 0) {
        console.log(`   ${regime}: ${stats.trades} trades | ${stats.winRate.toFixed(1)}% win rate | ${stats.avgPnL.toFixed(2)} avg P&L`);
      }
    });

    if (totalValue >= this.TARGET_TOTAL) {
      console.log("\n🎉 CONGRATULATIONS! 25,000 USDT TARGET ACHIEVED!");
      console.log(`🚀 Your ${this.initialBalance} USDT became ${totalValue.toFixed(2)} USDT!`);
      console.log(`💰 That's a ${returnMultiplier.toFixed(0)}x return!`);
    } else {
      console.log(`\n📈 Progress: ${(totalValue / this.TARGET_TOTAL * 100).toFixed(1)}% towards 25K target`);
      console.log(`💡 Strategy shows ${returnMultiplier.toFixed(2)}x return potential`);
      console.log(`🎯 Need ${(this.TARGET_TOTAL - totalValue).toFixed(2)} more USDT to reach target`);
    }

    // Save results
    const result = {
      strategy: "Adaptive 25K Strategy",
      initialBalance: this.initialBalance,
      finalBalance,
      totalWithdrawn: this.totalWithdrawn,
      totalValue,
      totalReturn,
      returnPercent,
      returnMultiplier,
      maxBalance: this.maxBalance,
      maxDrawdown: this.maxDrawdown * 100,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,
      avgWin,
      avgLoss,
      profitFactor,
      targetAchieved: totalValue >= this.TARGET_TOTAL,
      targetProgress: (totalValue / this.TARGET_TOTAL) * 100,
      trades: this.trades,
      regimeStats,
      dataStart: this.data[0]?.time,
      dataEnd: this.data[this.data.length - 1]?.time,
    };

    const resultsFile = `./results/backtests/adaptive_25k_${Date.now()}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to: ${resultsFile}`);
  }
}

// Run the strategy
async function main() {
  const strategy = new Adaptive25kStrategy(40);

  try {
    const dataFile = './data/history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv';
    console.log(`🚀 Running Adaptive 25K Strategy on: ${dataFile}`);

    await strategy.loadData(dataFile);
    await strategy.runBacktest();
  } catch (error) {
    console.error('❌ Error running backtest:', error);
  }
}

main().catch(console.error);