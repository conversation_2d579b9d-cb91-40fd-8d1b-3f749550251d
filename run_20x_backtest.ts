import { Ultimate20xStrategy } from './src/strategies/Ultimate20xStrategy.js';
import * as fs from 'fs';

async function main() {
  console.log('🚀 Starting Ultimate 20X Strategy Backtest');
  console.log('Dataset: history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv');
  
  const strategy = new Ultimate20xStrategy(100); // Start with 100 USDT
  
  try {
    // Load your specific dataset
    const dataFile = './data/history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv';
    
    await strategy.loadData(dataFile);
    await strategy.runBacktest(); // This will print all results
    
    // Get results for saving
    const result = strategy.getResults();
    
    // Save results to file
    const resultsFile = `./results/backtests/ultimate_20x_history_${Date.now()}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to: ${resultsFile}`);
    
    // Summary
    console.log('\n📊 QUICK SUMMARY:');
    console.log(`💰 Final Balance: ${result.finalBalance.toFixed(2)} USDT`);
    console.log(`🚀 Return: ${result.returnMultiplier.toFixed(2)}x`);
    console.log(`🎯 Target (21x): ${result.targetAchieved ? '✅ ACHIEVED' : '❌ Not reached'}`);
    console.log(`📈 Progress: ${((result.returnMultiplier / 21) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('❌ Error running backtest:', error);
  }
}

main().catch(console.error);
