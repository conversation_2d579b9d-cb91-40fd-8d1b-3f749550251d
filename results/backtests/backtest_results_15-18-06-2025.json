{"newDataset": {"name": "Jun 15-18, 2025", "target": 800, "result": {"dataset": "Jun 15-18, 2025", "initialBalance": 100, "finalBalance": 113.02713247825355, "totalReturn": 13.027132478253549, "totalReturnPercent": 13.027132478253547, "totalTrades": 2, "winningTrades": 1, "losingTrades": 1, "winRate": 50, "maxDrawdown": 3.999999999999995, "sharpeRatio": -0.1442051396353196, "trades": [{"entryTime": "2025-06-16T22:30:00.000Z", "exitTime": "2025-06-17T00:00:00.000Z", "side": "SHORT", "entryPrice": 107457.41, "exitPrice": 106231.89, "size": 1555.2, "pnl": 17.7365963315141, "pnlPercent": 45.618817725087695, "leverage": 40, "entryReason": "DEAD_MARKET_BREAKDOWN", "exitReason": "DEAD_MARKET_QUICK_PROFIT"}, {"entryTime": "2025-06-17T16:45:00.000Z", "exitTime": "2025-06-18T03:30:00.000Z", "side": "SHORT", "entryPrice": 103630.67, "exitPrice": 105210.85, "size": 1831.0395461477074, "pnl": -4.709463853260565, "pnlPercent": -60.99275436509317, "leverage": 40, "entryReason": "DEAD_MARKET_MOMENTUM_SHORT", "exitReason": "STOP_LOSS"}], "avgWin": 17.7365963315141, "avgLoss": 4.709463853260565, "profitFactor": 3.7661604131931683, "maxConsecutiveLosses": 1, "maxConsecutiveWins": 1}, "targetAchieved": false, "progressPercent": 14.1}}