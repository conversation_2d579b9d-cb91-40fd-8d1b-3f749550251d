<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conservative 20X Strategy - Interactive Timeline</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .positive { color: #27ae60; }
        .negative { color: #e74c3c; }
        .neutral { color: #3498db; }
        
        .chart-container {
            padding: 30px;
            background: white;
        }
        
        .chart-wrapper {
            position: relative;
            height: 500px;
            margin-bottom: 30px;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
        
        .trade-details {
            background: #f8f9fa;
            padding: 30px;
            border-top: 1px solid #dee2e6;
        }
        
        .trade-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .trade-table th {
            background: #2c3e50;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .trade-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .trade-table tr:hover {
            background: #f8f9fa;
        }
        
        .profit { background-color: #d4edda !important; }
        .loss { background-color: #f8d7da !important; }
        .withdrawal { background-color: #fff3cd !important; }
        
        .section-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 Conservative 20X Strategy Timeline</h1>
            <p>Interactive Performance Analysis with Profit Withdrawal System</p>
            <p><strong>Dataset:</strong> history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value positive">14.93</div>
                <div class="stat-label">Total Value (USDT)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value neutral">6.40</div>
                <div class="stat-label">Withdrawn (USDT)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value neutral">8.53</div>
                <div class="stat-label">Final Balance (USDT)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value negative">0.37x</div>
                <div class="stat-label">Return Multiplier</div>
            </div>
            <div class="stat-card">
                <div class="stat-value positive">48.0%</div>
                <div class="stat-label">Win Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-value neutral">25</div>
                <div class="stat-label">Total Trades</div>
            </div>
        </div>
        
        <div class="chart-container">
            <h2 class="section-title">📈 Balance & Trading Timeline</h2>
            <div class="chart-wrapper">
                <canvas id="timelineChart"></canvas>
            </div>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #27ae60;"></div>
                    <span>Winning Trades</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #e74c3c;"></div>
                    <span>Losing Trades</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f39c12;"></div>
                    <span>Profit Withdrawals</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #3498db;"></div>
                    <span>Balance Line</span>
                </div>
            </div>
        </div>
        
        <div class="trade-details">
            <h2 class="section-title">📊 Detailed Trade History</h2>
            <div style="overflow-x: auto;">
                <table class="trade-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Side</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>P&L (USDT)</th>
                            <th>P&L (%)</th>
                            <th>Balance After</th>
                            <th>Reason</th>
                        </tr>
                    </thead>
                    <tbody id="tradeTableBody">
                        <!-- Trade data will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Trade data from the backtest results
        const trades = [
            {date: '2024-02-02T09:00:00.000Z', side: 'LONG', entryPrice: 49676.01, exitPrice: 49796.01, pnl: 2.43, pnlPercent: 12.17, balanceAfter: 42.43, reason: 'RSI_OVERBOUGHT'},
            {date: '2024-02-03T15:30:00.000Z', side: 'SHORT', entryPrice: 49126, exitPrice: 49046, pnl: 1.72, pnlPercent: 8.12, balanceAfter: 44.16, reason: 'RSI_OVERSOLD'},
            {date: '2024-02-05T12:15:00.000Z', side: 'LONG', entryPrice: 50758, exitPrice: 50858, pnl: 2.24, pnlPercent: 10.15, balanceAfter: 46.40, reason: 'RSI_OVERBOUGHT'},
            {date: '2024-02-14T17:00:00.000Z', side: 'WITHDRAWAL', entryPrice: 0, exitPrice: 0, pnl: -6.40, pnlPercent: 0, balanceAfter: 40.00, reason: 'PROFIT_WITHDRAWAL'},
            {date: '2024-02-16T10:30:00.000Z', side: 'SHORT', entryPrice: 50771.32, exitPrice: 50801.32, pnl: -0.69, pnlPercent: -3.44, balanceAfter: 39.31, reason: 'RSI_OVERSOLD'},
            {date: '2024-02-20T14:45:00.000Z', side: 'LONG', entryPrice: 52874.67, exitPrice: 52081.67, pnl: -8.77, pnlPercent: -44.63, balanceAfter: 30.54, reason: 'STOP_LOSS'},
            {date: '2024-02-22T11:20:00.000Z', side: 'LONG', entryPrice: 52350.77, exitPrice: 52450.77, pnl: 1.40, pnlPercent: 9.15, balanceAfter: 31.94, reason: 'RSI_OVERBOUGHT'},
            {date: '2024-02-25T16:10:00.000Z', side: 'LONG', entryPrice: 53509.44, exitPrice: 53409.44, pnl: -1.89, pnlPercent: -11.81, balanceAfter: 30.05, reason: 'RSI_OVERBOUGHT'},
            {date: '2024-03-01T13:25:00.000Z', side: 'LONG', entryPrice: 55768.41, exitPrice: 56268.41, pnl: 3.95, pnlPercent: 26.30, balanceAfter: 34.00, reason: 'RSI_OVERBOUGHT'},
            {date: '2024-03-05T09:40:00.000Z', side: 'LONG', entryPrice: 56355.13, exitPrice: 56359.13, pnl: 0.01, pnlPercent: 0.07, balanceAfter: 34.02, reason: 'RSI_OVERBOUGHT'},
            {date: '2024-03-08T15:55:00.000Z', side: 'LONG', entryPrice: 57383.87, exitPrice: 56523.87, pnl: -6.75, pnlPercent: -39.66, balanceAfter: 27.27, reason: 'STOP_LOSS'},
            {date: '2024-03-12T12:30:00.000Z', side: 'LONG', entryPrice: 59448.42, exitPrice: 59348.42, pnl: -1.05, pnlPercent: -7.70, balanceAfter: 26.22, reason: 'RSI_OVERBOUGHT'},
            {date: '2024-03-15T18:15:00.000Z', side: 'LONG', entryPrice: 63690.12, exitPrice: 62735.12, pnl: -9.78, pnlPercent: -74.57, balanceAfter: 16.45, reason: 'STOP_LOSS'},
            {date: '2024-03-18T14:20:00.000Z', side: 'LONG', entryPrice: 63199.99, exitPrice: 62251.99, pnl: -3.14, pnlPercent: -38.24, balanceAfter: 13.30, reason: 'STOP_LOSS'},
            {date: '2024-03-22T11:45:00.000Z', side: 'SHORT', entryPrice: 60960.32, exitPrice: 61874.32, pnl: -2.72, pnlPercent: -40.94, balanceAfter: 10.58, reason: 'STOP_LOSS'},
            {date: '2024-03-25T16:30:00.000Z', side: 'LONG', entryPrice: 64793.99, exitPrice: 65043.99, pnl: 0.20, pnlPercent: 3.86, balanceAfter: 10.78, reason: 'RSI_OVERBOUGHT'},
            {date: '2024-03-28T13:10:00.000Z', side: 'LONG', entryPrice: 65456.7, exitPrice: 68400.7, pnl: 6.09, pnlPercent: 113.02, balanceAfter: 16.87, reason: 'TAKE_PROFIT'},
            {date: '2024-04-02T10:25:00.000Z', side: 'LONG', entryPrice: 68360.96, exitPrice: 67335.96, pnl: -3.67, pnlPercent: -43.55, balanceAfter: 13.20, reason: 'STOP_LOSS'},
            {date: '2024-04-05T15:40:00.000Z', side: 'SHORT', entryPrice: 62330, exitPrice: 61380, pnl: 2.43, pnlPercent: 36.86, balanceAfter: 15.63, reason: 'RSI_OVERSOLD'},
            {date: '2024-04-08T12:55:00.000Z', side: 'SHORT', entryPrice: 61410.98, exitPrice: 62330.98, pnl: -5.22, pnlPercent: -66.83, balanceAfter: 10.41, reason: 'STOP_LOSS'},
            {date: '2024-04-12T09:20:00.000Z', side: 'LONG', entryPrice: 65347.62, exitPrice: 68287.62, pnl: 5.97, pnlPercent: 114.77, balanceAfter: 16.38, reason: 'TAKE_PROFIT'},
            {date: '2024-04-15T14:35:00.000Z', side: 'LONG', entryPrice: 69510.79, exitPrice: 68467.79, pnl: -3.79, pnlPercent: -46.28, balanceAfter: 12.59, reason: 'STOP_LOSS'},
            {date: '2024-04-18T11:50:00.000Z', side: 'LONG', entryPrice: 70447.12, exitPrice: 70870.12, pnl: 0.38, pnlPercent: 6.01, balanceAfter: 12.97, reason: 'RSI_OVERBOUGHT'},
            {date: '2024-04-22T16:05:00.000Z', side: 'SHORT', entryPrice: 70606.97, exitPrice: 67296.97, pnl: 3.11, pnlPercent: 47.90, balanceAfter: 16.08, reason: 'RSI_OVERSOLD'},
            {date: '2024-04-25T13:20:00.000Z', side: 'SHORT', entryPrice: 69254.26, exitPrice: 70294.26, pnl: -5.33, pnlPercent: -66.36, balanceAfter: 10.74, reason: 'STOP_LOSS'},
            {date: '2024-04-28T10:35:00.000Z', side: 'SHORT', entryPrice: 68990.01, exitPrice: 70025.01, pnl: -2.21, pnlPercent: -41.10, balanceAfter: 8.53, reason: 'STOP_LOSS'}
        ];

        // Prepare data for Chart.js
        const balanceData = [];
        const tradePoints = [];
        let currentBalance = 40;

        trades.forEach((trade, index) => {
            const date = new Date(trade.date);
            
            if (trade.side === 'WITHDRAWAL') {
                // Add withdrawal point
                tradePoints.push({
                    x: date,
                    y: currentBalance,
                    type: 'withdrawal',
                    trade: trade,
                    index: index + 1
                });
                currentBalance = trade.balanceAfter;
            } else {
                // Add trade point
                tradePoints.push({
                    x: date,
                    y: trade.balanceAfter,
                    type: trade.pnl > 0 ? 'win' : 'loss',
                    trade: trade,
                    index: index + 1
                });
                currentBalance = trade.balanceAfter;
            }
            
            balanceData.push({
                x: date,
                y: currentBalance
            });
        });

        // Create the chart
        const ctx = document.getElementById('timelineChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [
                    {
                        label: 'Balance',
                        data: balanceData,
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 8
                    },
                    {
                        label: 'Winning Trades',
                        data: tradePoints.filter(p => p.type === 'win'),
                        backgroundColor: '#27ae60',
                        borderColor: '#27ae60',
                        pointRadius: 8,
                        pointHoverRadius: 12,
                        showLine: false
                    },
                    {
                        label: 'Losing Trades',
                        data: tradePoints.filter(p => p.type === 'loss'),
                        backgroundColor: '#e74c3c',
                        borderColor: '#e74c3c',
                        pointRadius: 8,
                        pointHoverRadius: 12,
                        showLine: false
                    },
                    {
                        label: 'Profit Withdrawals',
                        data: tradePoints.filter(p => p.type === 'withdrawal'),
                        backgroundColor: '#f39c12',
                        borderColor: '#f39c12',
                        pointRadius: 12,
                        pointHoverRadius: 16,
                        showLine: false,
                        pointStyle: 'triangle'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'point'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return new Date(context[0].parsed.x).toLocaleDateString();
                            },
                            label: function(context) {
                                const point = context.raw;
                                if (point.trade) {
                                    const trade = point.trade;
                                    if (trade.side === 'WITHDRAWAL') {
                                        return `Withdrawal: ${Math.abs(trade.pnl).toFixed(2)} USDT`;
                                    } else {
                                        return [
                                            `${trade.side} Trade`,
                                            `P&L: ${trade.pnl.toFixed(2)} USDT (${trade.pnlPercent.toFixed(2)}%)`,
                                            `Entry: ${trade.entryPrice.toFixed(2)}`,
                                            `Exit: ${trade.exitPrice.toFixed(2)}`,
                                            `Balance: ${trade.balanceAfter.toFixed(2)} USDT`,
                                            `Reason: ${trade.reason}`
                                        ];
                                    }
                                }
                                return `Balance: ${context.parsed.y.toFixed(2)} USDT`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day',
                            displayFormats: {
                                day: 'MMM dd'
                            }
                        },
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Balance (USDT)'
                        },
                        beginAtZero: true
                    }
                }
            }
        });

        // Populate trade table
        const tableBody = document.getElementById('tradeTableBody');
        trades.forEach((trade, index) => {
            const row = document.createElement('tr');
            
            if (trade.side === 'WITHDRAWAL') {
                row.className = 'withdrawal';
            } else if (trade.pnl > 0) {
                row.className = 'profit';
            } else {
                row.className = 'loss';
            }
            
            const date = new Date(trade.date).toLocaleDateString();
            const type = trade.side === 'WITHDRAWAL' ? 'Withdrawal' : 'Trade';
            const side = trade.side === 'WITHDRAWAL' ? '-' : trade.side;
            const entryPrice = trade.side === 'WITHDRAWAL' ? '-' : trade.entryPrice.toFixed(2);
            const exitPrice = trade.side === 'WITHDRAWAL' ? '-' : trade.exitPrice.toFixed(2);
            const pnl = trade.pnl.toFixed(2);
            const pnlPercent = trade.side === 'WITHDRAWAL' ? '-' : trade.pnlPercent.toFixed(2) + '%';
            
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${date}</td>
                <td>${type}</td>
                <td>${side}</td>
                <td>${entryPrice}</td>
                <td>${exitPrice}</td>
                <td class="${trade.pnl > 0 ? 'positive' : 'negative'}">${pnl}</td>
                <td>${pnlPercent}</td>
                <td>${trade.balanceAfter.toFixed(2)}</td>
                <td>${trade.reason}</td>
            `;
            
            tableBody.appendChild(row);
        });
    </script>
</body>
</html>
