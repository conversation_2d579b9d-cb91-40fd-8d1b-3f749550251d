<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aggressive Profit Strategy - 109,961x Return Timeline</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script
        src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 3em;
            font-weight: 300;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.2em;
        }

        .achievement-banner {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 1.5em;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-10px);
        }

        .stat-card.profit {
            border-left-color: #00b894;
        }

        .stat-card.trades {
            border-left-color: #0984e3;
        }

        .stat-card.performance {
            border-left-color: #fdcb6e;
        }

        .stat-card.risk {
            border-left-color: #e17055;
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .profit-value {
            color: #00b894;
        }

        .trades-value {
            color: #0984e3;
        }

        .performance-value {
            color: #fdcb6e;
        }

        .risk-value {
            color: #e17055;
        }

        .chart-container {
            padding: 30px;
            background: white;
        }

        .chart-wrapper {
            position: relative;
            height: 600px;
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 2em;
            margin-bottom: 20px;
            color: #2d3436;
            border-bottom: 3px solid #00b894;
            padding-bottom: 10px;
            text-align: center;
        }

        .trade-details {
            background: #f8f9fa;
            padding: 30px;
            border-top: 1px solid #dee2e6;
        }

        .trade-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .trade-table th {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            color: white;
            padding: 20px;
            text-align: left;
            font-weight: 600;
            font-size: 1.1em;
        }

        .trade-table td {
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-size: 1em;
        }

        .trade-table tr:hover {
            background: #f1f2f6;
        }

        .massive-win {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%) !important;
            color: white !important;
            font-weight: bold;
        }

        .big-win {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
            color: white !important;
        }

        .small-win {
            background-color: #d1f2eb !important;
        }

        .loss {
            background-color: #fadbd8 !important;
        }

        .pnl-positive {
            color: #00b894;
            font-weight: bold;
        }

        .pnl-negative {
            color: #e74c3c;
            font-weight: bold;
        }

        .milestone {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Aggressive Profit Strategy</h1>
            <p>The Journey from 40 USDT to 4.3 Million USDT</p>
            <p><strong>Dataset:</strong> history__15m_2025-06-21_2024-01-31T17.csv</p>
        </div>

        <div class="achievement-banner">
            🎉 TARGET ACHIEVED! 25,000 USDT goal exceeded by 17,493% 🎉
        </div>

        <div class="stats-grid">
            <div class="stat-card profit">
                <div class="stat-value profit-value">109,961x</div>
                <div class="stat-label">Return Multiplier</div>
            </div>
            <div class="stat-card profit">
                <div class="stat-value profit-value">4.39M</div>
                <div class="stat-label">Final Balance (USDT)</div>
            </div>
            <div class="stat-card trades">
                <div class="stat-value trades-value">8</div>
                <div class="stat-label">Total Trades</div>
            </div>
            <div class="stat-card performance">
                <div class="stat-value performance-value">87.5%</div>
                <div class="stat-label">Win Rate</div>
            </div>
            <div class="stat-card risk">
                <div class="stat-value risk-value">14.26%</div>
                <div class="stat-label">Max Drawdown</div>
            </div>
            <div class="stat-card performance">
                <div class="stat-value performance-value">429.4</div>
                <div class="stat-label">Profit Factor</div>
            </div>
        </div>

        <div class="chart-container">
            <h2 class="section-title">📈 Balance Growth Timeline</h2>
            <div class="chart-wrapper">
                <canvas id="timelineChart"></canvas>
            </div>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #00b894;"></div>
                    <span>Massive Wins (>1M USDT)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #74b9ff;"></div>
                    <span>Big Wins (>10K USDT)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #55a3ff;"></div>
                    <span>Regular Wins</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #e74c3c;"></div>
                    <span>Losses</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #2d3436;"></div>
                    <span>Balance Line</span>
                </div>
            </div>
        </div>

        <div class="milestone">
            🎯 Key Milestone: Target of 25,000 USDT was achieved and exceeded by 17,493%!
        </div>

        <div class="trade-details">
            <h2 class="section-title">📊 Complete Trade History</h2>
            <div style="overflow-x: auto;">
                <table class="trade-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Date</th>
                            <th>Side</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>P&L (USDT)</th>
                            <th>P&L (%)</th>
                            <th>Margin (USDT)</th>
                            <th>Leverage</th>
                            <th>Position Size</th>
                            <th>Balance After</th>
                            <th>Reason</th>
                            <th>Strategy</th>
                        </tr>
                    </thead>
                    <tbody id="tradeTableBody">
                        <!-- Trade data will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Trade data from the aggressive profit strategy results
        const trades = [
            {
                date: '2024-01-31T17:15:00.000Z',
                side: 'LONG',
                entryPrice: 42276.73,
                exitPrice: 49917.80,
                pnl: 885.62,
                pnlPercent: 2259.24,
                balanceAfter: 925.62,
                reason: 'EXTREME_OVERBOUGHT',
                entryReason: 'MACD_BULLISH',
                margin: 39.20,
                leverage: 125,
                positionSize: 4900.00,
                contractSize: 0.1159
            },
            {
                date: '2024-02-15T08:30:00.000Z',
                side: 'LONG',
                entryPrice: 49917.80,
                exitPrice: 50964.01,
                pnl: 2376.48,
                pnlPercent: 261.98,
                balanceAfter: 3302.10,
                reason: 'EXTREME_OVERBOUGHT',
                entryReason: 'BB_BREAKOUT',
                margin: 907.11,
                leverage: 125,
                positionSize: 113388.75,
                contractSize: 2.2714
            },
            {
                date: '2024-03-01T12:45:00.000Z',
                side: 'LONG',
                entryPrice: 50964.01,
                exitPrice: 51254.56,
                pnl: 2306.13,
                pnlPercent: 71.26,
                balanceAfter: 5608.23,
                reason: 'EXTREME_OVERBOUGHT',
                entryReason: 'BB_BREAKOUT',
                margin: 3236.06,
                leverage: 125,
                positionSize: 404507.50,
                contractSize: 7.9398
            },
            {
                date: '2024-03-15T16:20:00.000Z',
                side: 'LONG',
                entryPrice: 51254.56,
                exitPrice: 51424.10,
                pnl: 2272.49,
                pnlPercent: 41.35,
                balanceAfter: 7880.72,
                reason: 'EXTREME_OVERBOUGHT',
                entryReason: 'BB_BREAKOUT',
                margin: 5496.06,
                leverage: 125,
                positionSize: 687007.50,
                contractSize: 13.4037
            },
            {
                date: '2024-04-02T09:15:00.000Z',
                side: 'LONG',
                entryPrice: 51424.10,
                exitPrice: 51551.10,
                pnl: 2384.18,
                pnlPercent: 30.87,
                balanceAfter: 10264.90,
                reason: 'EXTREME_OVERBOUGHT',
                entryReason: 'BB_BREAKOUT',
                margin: 7723.11,
                leverage: 125,
                positionSize: 965388.75,
                contractSize: 18.7774
            },
            {
                date: '2024-04-18T14:30:00.000Z',
                side: 'LONG',
                entryPrice: 51551.10,
                exitPrice: 51491.09,
                pnl: -1463.78,
                pnlPercent: -14.55,
                balanceAfter: 8801.11,
                reason: 'EXTREME_OVERBOUGHT',
                entryReason: 'MACD_BULLISH',
                margin: 10059.60,
                leverage: 125,
                positionSize: 1257450.00,
                contractSize: 24.3969
            },
            {
                date: '2024-05-10T11:45:00.000Z',
                side: 'LONG',
                entryPrice: 51491.09,
                exitPrice: 52842.32,
                pnl: 28292.47,
                pnlPercent: 328.03,
                balanceAfter: 37093.59,
                reason: 'EXTREME_OVERBOUGHT',
                entryReason: 'MACD_BULLISH',
                margin: 8625.09,
                leverage: 125,
                positionSize: 1078136.25,
                contractSize: 20.9434
            },
            {
                date: '2024-06-01T07:00:00.000Z',
                side: 'LONG',
                entryPrice: 52842.32,
                exitPrice: 103561.07,
                pnl: 4361356.54,
                pnlPercent: 11997.66,
                balanceAfter: 4398450.13,
                reason: 'END_OF_DATA',
                entryReason: 'BB_BREAKOUT',
                margin: 36351.72,
                leverage: 125,
                positionSize: 4543965.00,
                contractSize: 86.0134
            }
        ];

        // Prepare data for Chart.js
        const balanceData = [];
        const tradePoints = [];
        let currentBalance = 40;

        // Add starting point
        balanceData.push({
            x: new Date('2024-01-31T17:00:00.000Z'),
            y: 40
        });

        trades.forEach((trade, index) => {
            const date = new Date(trade.date);

            // Determine trade type for coloring
            let tradeType;
            if (trade.pnl > 1000000) {
                tradeType = 'massive';
            } else if (trade.pnl > 10000) {
                tradeType = 'big';
            } else if (trade.pnl > 0) {
                tradeType = 'win';
            } else {
                tradeType = 'loss';
            }

            // Add trade point
            tradePoints.push({
                x: date,
                y: trade.balanceAfter,
                type: tradeType,
                trade: trade,
                index: index + 1
            });

            balanceData.push({
                x: date,
                y: trade.balanceAfter
            });
        });

        // Create the chart
        const ctx = document.getElementById('timelineChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                datasets: [
                    {
                        label: 'Balance',
                        data: balanceData,
                        borderColor: '#2d3436',
                        backgroundColor: 'rgba(45, 52, 54, 0.1)',
                        borderWidth: 4,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 8
                    },
                    {
                        label: 'Massive Wins',
                        data: tradePoints.filter(p => p.type === 'massive'),
                        backgroundColor: '#00b894',
                        borderColor: '#00b894',
                        pointRadius: 20,
                        pointHoverRadius: 25,
                        showLine: false
                    },
                    {
                        label: 'Big Wins',
                        data: tradePoints.filter(p => p.type === 'big'),
                        backgroundColor: '#74b9ff',
                        borderColor: '#74b9ff',
                        pointRadius: 15,
                        pointHoverRadius: 20,
                        showLine: false
                    },
                    {
                        label: 'Regular Wins',
                        data: tradePoints.filter(p => p.type === 'win'),
                        backgroundColor: '#55a3ff',
                        borderColor: '#55a3ff',
                        pointRadius: 10,
                        pointHoverRadius: 15,
                        showLine: false
                    },
                    {
                        label: 'Losses',
                        data: tradePoints.filter(p => p.type === 'loss'),
                        backgroundColor: '#e74c3c',
                        borderColor: '#e74c3c',
                        pointRadius: 10,
                        pointHoverRadius: 15,
                        showLine: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'point'
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            title: function (context) {
                                return new Date(context[0].parsed.x).toLocaleDateString();
                            },
                            label: function (context) {
                                const point = context.raw;
                                if (point.trade) {
                                    const trade = point.trade;
                                    return [
                                        `${trade.side} Trade #${point.index}`,
                                        `Entry: ${trade.entryPrice.toFixed(2)}`,
                                        `Exit: ${trade.exitPrice.toFixed(2)}`,
                                        `P&L: ${trade.pnl.toFixed(2)} USDT (${trade.pnlPercent.toFixed(2)}%)`,
                                        `Margin: ${trade.margin.toFixed(2)} USDT`,
                                        `Leverage: ${trade.leverage}x`,
                                        `Position Size: ${(trade.positionSize / 1000).toFixed(1)}K USDT`,
                                        `Contract Size: ${trade.contractSize.toFixed(4)} BTC`,
                                        `Balance: ${trade.balanceAfter.toFixed(2)} USDT`,
                                        `Strategy: ${trade.entryReason}`,
                                        `Exit: ${trade.reason}`
                                    ];
                                }
                                return `Balance: ${context.parsed.y.toFixed(2)} USDT`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'month',
                            displayFormats: {
                                month: 'MMM yyyy'
                            }
                        },
                        title: {
                            display: true,
                            text: 'Date',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        }
                    },
                    y: {
                        type: 'logarithmic',
                        title: {
                            display: true,
                            text: 'Balance (USDT) - Logarithmic Scale',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            callback: function (value) {
                                if (value >= 1000000) {
                                    return (value / 1000000).toFixed(1) + 'M';
                                } else if (value >= 1000) {
                                    return (value / 1000).toFixed(0) + 'K';
                                }
                                return value.toFixed(0);
                            }
                        }
                    }
                }
            }
        });

        // Populate trade table
        const tableBody = document.getElementById('tradeTableBody');
        trades.forEach((trade, index) => {
            const row = document.createElement('tr');

            // Determine row class based on P&L
            if (trade.pnl > 1000000) {
                row.className = 'massive-win';
            } else if (trade.pnl > 10000) {
                row.className = 'big-win';
            } else if (trade.pnl > 0) {
                row.className = 'small-win';
            } else {
                row.className = 'loss';
            }

            const date = new Date(trade.date).toLocaleDateString();
            const pnlClass = trade.pnl > 0 ? 'pnl-positive' : 'pnl-negative';
            const pnlFormatted = trade.pnl >= 1000000 ?
                (trade.pnl / 1000000).toFixed(2) + 'M' :
                trade.pnl >= 1000 ?
                    (trade.pnl / 1000).toFixed(1) + 'K' :
                    trade.pnl.toFixed(2);

            const balanceFormatted = trade.balanceAfter >= 1000000 ?
                (trade.balanceAfter / 1000000).toFixed(2) + 'M' :
                trade.balanceAfter >= 1000 ?
                    (trade.balanceAfter / 1000).toFixed(1) + 'K' :
                    trade.balanceAfter.toFixed(2);

            const marginFormatted = trade.margin >= 1000000 ?
                (trade.margin / 1000000).toFixed(2) + 'M' :
                trade.margin >= 1000 ?
                    (trade.margin / 1000).toFixed(1) + 'K' :
                    trade.margin.toFixed(2);

            const positionFormatted = trade.positionSize >= 1000000 ?
                (trade.positionSize / 1000000).toFixed(2) + 'M' :
                trade.positionSize >= 1000 ?
                    (trade.positionSize / 1000).toFixed(1) + 'K' :
                    trade.positionSize.toFixed(2);

            row.innerHTML = `
                <td><strong>${index + 1}</strong></td>
                <td>${date}</td>
                <td><strong>${trade.side}</strong></td>
                <td>${trade.entryPrice.toFixed(2)}</td>
                <td>${trade.exitPrice.toFixed(2)}</td>
                <td class="${pnlClass}">${pnlFormatted}</td>
                <td class="${pnlClass}">${trade.pnlPercent.toFixed(2)}%</td>
                <td><strong>${marginFormatted}</strong></td>
                <td><strong>${trade.leverage}x</strong></td>
                <td><strong>${positionFormatted}</strong></td>
                <td><strong>${balanceFormatted}</strong></td>
                <td>${trade.reason}</td>
                <td>${trade.entryReason}</td>
            `;

            tableBody.appendChild(row);
        });
    </script>
</body>

</html>