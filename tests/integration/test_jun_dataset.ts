#!/usr/bin/env node

import fs from "fs";
import csv from "csv-parser";
import { RSI, EMA } from "technicalindicators";

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Trade {
  entryTime: string;
  entryPrice: number;
  exitTime: string;
  exitPrice: number;
  pumpPercent: number;
  leveragedGain: number;
  profit: number;
  signals: string[];
  rsi: number;
  volumeRatio: number;
}

class JunDatasetTester {
  private data: CandleData[] = [];
  private LEVERAGE = 125;
  private MARGIN = 20;
  private INITIAL_BALANCE = 100;

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          console.log(`📅 Date range: ${results[0]?.time} to ${results[results.length - 1]?.time}`);
          resolve();
        })
        .on("error", reject);
    });
  }

  private calculateIndicators(index: number): any {
    const lookback = Math.min(50, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);
    
    const closes = candles.map(c => c.close);
    const volumes = candles.map(c => c.volume);

    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 8, values: closes });
    const emaSlowValues = EMA.calculate({ period: 21, values: closes });

    const avgVolume = volumes.slice(-20).reduce((sum, vol) => sum + vol, 0) / Math.min(20, volumes.length);

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast: emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      avgVolume
    };
  }

  private checkPumpSignals(index: number, indicators: any, candle: CandleData): string[] {
    const signals: string[] = [];
    
    // Core pump signals based on our proven model
    if (candle.close > indicators.emaFast) signals.push("ABOVE_EMA8");
    if (indicators.emaFast > indicators.emaSlow) signals.push("EMA_BULLISH");
    
    // RSI conditions
    if (indicators.rsi >= 25 && indicators.rsi <= 65) signals.push("RSI_OPTIMAL");
    if (indicators.rsi < 30) signals.push("RSI_OVERSOLD");
    
    // Volume conditions
    const volumeRatio = candle.volume / indicators.avgVolume;
    if (volumeRatio >= 1.1) signals.push("VOLUME_ABOVE_AVG");
    if (volumeRatio >= 2.0) signals.push("HIGH_VOLUME");
    if (volumeRatio >= 3.0) signals.push("VOLUME_EXPLOSION");
    
    // Price action
    const priceChange = (candle.close - candle.open) / candle.open * 100;
    if (priceChange > 0.5) signals.push("GREEN_CANDLE");
    if (priceChange > 1.0) signals.push("STRONG_GREEN_CANDLE");
    
    return signals;
  }

  private isPumpEntry(signals: string[]): boolean {
    // Our proven model: EMA_BULLISH + ABOVE_EMA8 are the strongest signals
    const hasEMABullish = signals.includes("EMA_BULLISH");
    const hasAboveEMA8 = signals.includes("ABOVE_EMA8");
    const hasRSIOptimal = signals.includes("RSI_OPTIMAL");
    const hasVolumeConfirmation = signals.includes("VOLUME_ABOVE_AVG");
    
    // Entry criteria based on our analysis
    return hasEMABullish && hasAboveEMA8 && hasRSIOptimal && hasVolumeConfirmation;
  }

  async testJunDataset(): Promise<void> {
    console.log("\n🔍 TESTING JUN 1-19 DATASET");
    console.log("Applying proven pump prediction model");
    console.log("=" .repeat(60));
    
    await this.loadData("./data/jun_1-19_15m.csv");
    
    let balance = this.INITIAL_BALANCE;
    const trades: Trade[] = [];
    let potentialEntries = 0;
    
    console.log("\n🔍 Scanning for pump opportunities...");
    
    for (let i = 50; i < this.data.length - 20; i++) {
      const candle = this.data[i];
      const indicators = this.calculateIndicators(i);
      const signals = this.checkPumpSignals(i, indicators, candle);
      
      // Check if this is a pump entry signal
      if (this.isPumpEntry(signals)) {
        potentialEntries++;
        
        // Look ahead to find the pump
        let maxGain = 0;
        let exitPrice = candle.close;
        let exitTime = candle.time;
        
        // Check next 20 candles (5 hours) for pumps
        for (let j = i + 1; j < Math.min(i + 21, this.data.length); j++) {
          const futureCandle = this.data[j];
          const gain = (futureCandle.high - candle.close) / candle.close * 100;
          
          if (gain > maxGain) {
            maxGain = gain;
            exitPrice = futureCandle.high;
            exitTime = futureCandle.time;
          }
        }
        
        console.log(`📊 Entry signal at ${candle.time} | Price: $${candle.close.toFixed(2)} | RSI: ${indicators.rsi.toFixed(1)} | Max pump: ${maxGain.toFixed(2)}%`);
        
        // Only count as successful trade if pump >= 3%
        if (maxGain >= 3.0) {
          const leveragedGain = maxGain * this.LEVERAGE;
          const profit = this.MARGIN * (leveragedGain / 100);
          
          balance += profit;
          
          trades.push({
            entryTime: candle.time,
            entryPrice: candle.close,
            exitTime,
            exitPrice,
            pumpPercent: maxGain,
            leveragedGain,
            profit,
            signals,
            rsi: indicators.rsi,
            volumeRatio: candle.volume / indicators.avgVolume
          });
          
          console.log(`🚀 SUCCESSFUL TRADE: ${maxGain.toFixed(2)}% pump = ${leveragedGain.toFixed(0)}% leveraged gain = +${profit.toFixed(2)} USDT`);
        }
      }
    }
    
    const totalProfit = balance - this.INITIAL_BALANCE;
    const returnMultiplier = balance / this.INITIAL_BALANCE;
    const avgPumpSize = trades.length > 0 ? trades.reduce((sum, t) => sum + t.pumpPercent, 0) / trades.length : 0;
    const maxPump = trades.length > 0 ? Math.max(...trades.map(t => t.pumpPercent)) : 0;
    
    console.log("\n🏆 JUN 1-19 DATASET RESULTS");
    console.log("=" .repeat(60));
    console.log(`📊 Potential entries found: ${potentialEntries}`);
    console.log(`🚀 Successful trades: ${trades.length}`);
    console.log(`💰 Total profit: ${totalProfit.toFixed(2)} USDT`);
    console.log(`📈 Return multiplier: ${returnMultiplier.toFixed(2)}x`);
    console.log(`🎯 Win rate: ${trades.length > 0 ? '100%' : '0%'} (on qualifying trades)`);
    console.log(`📊 Average pump size: ${avgPumpSize.toFixed(2)}%`);
    console.log(`🔥 Maximum pump: ${maxPump.toFixed(2)}%`);
    
    if (trades.length > 0) {
      console.log("\n🏆 TOP TRADES:");
      trades
        .sort((a, b) => b.pumpPercent - a.pumpPercent)
        .slice(0, 5)
        .forEach((trade, index) => {
          console.log(`   ${index + 1}. ${trade.pumpPercent.toFixed(2)}% pump = +${trade.profit.toFixed(2)} USDT`);
          console.log(`      ${trade.entryTime} → ${trade.exitTime}`);
          console.log(`      $${trade.entryPrice.toFixed(2)} → $${trade.exitPrice.toFixed(2)}`);
        });
    }
    
    if (returnMultiplier >= 5) {
      console.log(`\n🎉 EXCELLENT! Jun dataset achieved ${returnMultiplier.toFixed(1)}x returns!`);
      console.log(`💡 This dataset is PROFITABLE for the pump prediction model!`);
    } else if (trades.length > 0) {
      console.log(`\n📈 GOOD! Jun dataset shows ${trades.length} profitable opportunities.`);
      console.log(`💡 Return: ${returnMultiplier.toFixed(2)}x - Decent performance!`);
    } else {
      console.log(`\n⚠️ Jun dataset shows limited pump opportunities.`);
      console.log(`💡 This period may have been low volatility or sideways market.`);
    }
    
    // Save results
    const result = {
      dataset: "jun_1-19_15m.csv",
      timestamp: new Date().toISOString(),
      totalCandles: this.data.length,
      potentialEntries,
      successfulTrades: trades.length,
      totalProfit,
      returnMultiplier,
      avgPumpSize,
      maxPump,
      trades
    };
    
    fs.writeFileSync("jun_dataset_results.json", JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to jun_dataset_results.json`);
  }
}

// Run test on Jun dataset
async function main() {
  const tester = new JunDatasetTester();
  await tester.testJunDataset();
}

main().catch(console.error);
