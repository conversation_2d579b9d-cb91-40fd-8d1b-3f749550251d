import { Conservative20xStrategy } from './src/strategies/Conservative20xStrategy.js';
import * as fs from 'fs';

async function main() {
  console.log('🏦 Starting Conservative 20X Strategy with Profit Withdrawal');
  console.log('Dataset: history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv');
  
  const strategy = new Conservative20xStrategy(40); // Start with 40 USDT
  
  try {
    // Load your specific dataset
    const dataFile = './data/history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv';
    
    await strategy.loadData(dataFile);
    await strategy.runBacktest(); // This will print all results
    
    // Get results for saving
    const result = strategy.getResults();
    
    // Save results to file
    const resultsFile = `./results/backtests/conservative_20x_${Date.now()}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to: ${resultsFile}`);
    
    // Summary
    console.log('\n📊 QUICK SUMMARY:');
    console.log(`💰 Final Balance: ${result.finalBalance.toFixed(2)} USDT`);
    console.log(`💸 Total Withdrawn: ${result.totalWithdrawn.toFixed(2)} USDT`);
    console.log(`💎 Total Value: ${result.totalValue.toFixed(2)} USDT`);
    console.log(`🚀 Return: ${result.returnMultiplier.toFixed(2)}x`);
    console.log(`🎯 Target (21x): ${result.targetAchieved ? '✅ ACHIEVED' : '❌ Not reached'}`);
    console.log(`📈 Progress: ${((result.returnMultiplier / 21) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('❌ Error running backtest:', error);
  }
}

main().catch(console.error);
