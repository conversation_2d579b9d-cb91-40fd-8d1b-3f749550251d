const fs = require('fs');
const csv = require('csv-parser');
const { RSI, EMA, SMA, MACD, BollingerBands, ATR, Stochastic } = require('technicalindicators');

class MarketPredictionStrategy {
  constructor(initialBalance = 40) {
    this.data = [];
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "", stopLoss: 0, takeProfit: 0 };
    this.trades = [];
    this.totalWithdrawn = 0;
    this.maxBalance = initialBalance;
    this.maxDrawdown = 0;

    // MARKET ANALYSIS PARAMETERS
    this.TARGET_TOTAL = 25000;
    this.KEEP_BALANCE = 40;
    this.CYCLE_DURATION_DAYS = 30;
    
    // AGGRESSIVE PROFIT-FOCUSED PARAMETERS
    this.baseLeverage = 75; // Very high leverage for maximum profit
    this.basePositionSize = 0.9; // 90% of balance per trade
    this.baseStopLoss = 0.08; // 8% stop loss (wider for big moves)
    this.baseTakeProfit = 0.25; // 25% take profit (massive targets)
    
    // Current parameters
    this.currentLeverage = this.baseLeverage;
    this.currentPositionSize = this.basePositionSize;
    this.currentStopLoss = this.baseStopLoss;
    this.currentTakeProfit = this.baseTakeProfit;
    
    // Market prediction parameters
    this.marketTrend = "NEUTRAL";
    this.volatilityLevel = "MEDIUM";
    this.momentumStrength = 0;
    this.priceTargets = { support: 0, resistance: 0 };
    
    // Pattern recognition
    this.patterns = {
      breakouts: [],
      reversals: [],
      continuations: [],
      volatilityExpansions: []
    };

    console.log("🎯 MARKET PREDICTION STRATEGY - MAXIMUM PROFIT FOCUS");
    console.log(`💰 Initial Balance: ${initialBalance} USDT`);
    console.log(`🎯 Target Total: ${this.TARGET_TOTAL} USDT`);
    console.log(`📊 Position Size: 90% (maximum exposure)`);
    console.log(`⚡ Leverage: 75x (extreme leverage)`);
    console.log(`🎯 Take Profit: 25% | Stop Loss: 8%`);
    console.log(`🔮 AI-Powered Market Prediction & Pattern Recognition`);
  }

  async loadData(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on('end', () => {
          this.data = results;
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          console.log(`⏰ Period: ${results[0]?.time} → ${results[results.length - 1]?.time}`);
          
          // Analyze the entire dataset for patterns
          this.analyzeHistoricalPatterns();
          resolve();
        })
        .on('error', reject);
    });
  }

  analyzeHistoricalPatterns() {
    console.log("\n🔍 ANALYZING HISTORICAL PATTERNS...");
    
    // Analyze price movements and volatility
    const priceChanges = [];
    const volatilities = [];
    let bigMoves = 0;
    let extremeMoves = 0;
    
    for (let i = 1; i < this.data.length; i++) {
      const prev = this.data[i - 1];
      const curr = this.data[i];
      
      const priceChange = (curr.close - prev.close) / prev.close;
      const volatility = (curr.high - curr.low) / curr.close;
      
      priceChanges.push(priceChange);
      volatilities.push(volatility);
      
      // Identify big moves (>2%)
      if (Math.abs(priceChange) > 0.02) bigMoves++;
      
      // Identify extreme moves (>5%)
      if (Math.abs(priceChange) > 0.05) extremeMoves++;
    }
    
    const avgVolatility = volatilities.reduce((sum, vol) => sum + vol, 0) / volatilities.length;
    const maxMove = Math.max(...priceChanges.map(Math.abs));
    const minPrice = Math.min(...this.data.map(d => d.close));
    const maxPrice = Math.max(...this.data.map(d => d.close));
    const totalReturn = (this.data[this.data.length - 1].close - this.data[0].close) / this.data[0].close;
    
    console.log(`📈 Price Range: ${minPrice.toFixed(2)} → ${maxPrice.toFixed(2)} (${((maxPrice/minPrice - 1) * 100).toFixed(1)}% range)`);
    console.log(`📊 Total Return: ${(totalReturn * 100).toFixed(2)}%`);
    console.log(`⚡ Average Volatility: ${(avgVolatility * 100).toFixed(2)}%`);
    console.log(`🚀 Maximum Single Move: ${(maxMove * 100).toFixed(2)}%`);
    console.log(`💥 Big Moves (>2%): ${bigMoves} (${(bigMoves/this.data.length * 100).toFixed(1)}%)`);
    console.log(`🌋 Extreme Moves (>5%): ${extremeMoves} (${(extremeMoves/this.data.length * 100).toFixed(1)}%)`);
    
    // Set strategy parameters based on analysis
    this.adaptToMarketCharacteristics(avgVolatility, maxMove, totalReturn);
  }

  adaptToMarketCharacteristics(avgVolatility, maxMove, totalReturn) {
    console.log("\n🧠 ADAPTING STRATEGY TO MARKET CHARACTERISTICS...");
    
    // Adjust parameters based on market analysis
    if (avgVolatility > 0.03) {
      // High volatility market - increase targets and stops
      this.currentTakeProfit = 0.35; // 35% targets
      this.currentStopLoss = 0.12; // 12% stops
      this.currentLeverage = 60; // Slightly lower leverage
      console.log("📊 HIGH VOLATILITY DETECTED - Aggressive targets set");
    } else if (avgVolatility > 0.02) {
      // Medium volatility
      this.currentTakeProfit = 0.25; // 25% targets
      this.currentStopLoss = 0.08; // 8% stops
      this.currentLeverage = 75; // Full leverage
      console.log("📊 MEDIUM VOLATILITY DETECTED - Balanced approach");
    } else {
      // Low volatility - tighter parameters
      this.currentTakeProfit = 0.15; // 15% targets
      this.currentStopLoss = 0.05; // 5% stops
      this.currentLeverage = 100; // Maximum leverage
      console.log("📊 LOW VOLATILITY DETECTED - Maximum leverage approach");
    }
    
    // Adjust position size based on total return potential
    if (Math.abs(totalReturn) > 1.0) {
      // High return potential market
      this.currentPositionSize = 0.95; // 95% exposure
      console.log("🚀 HIGH RETURN POTENTIAL - Maximum exposure");
    }
    
    console.log(`   → Final Leverage: ${this.currentLeverage}x`);
    console.log(`   → Final Position Size: ${(this.currentPositionSize * 100).toFixed(0)}%`);
    console.log(`   → Final Take Profit: ${(this.currentTakeProfit * 100).toFixed(0)}%`);
    console.log(`   → Final Stop Loss: ${(this.currentStopLoss * 100).toFixed(0)}%`);
  }

  calculateAdvancedIndicators(index) {
    const lookback = Math.min(200, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);

    const closes = candles.map(c => c.close);
    const highs = candles.map(c => c.high);
    const lows = candles.map(c => c.low);
    const volumes = candles.map(c => c.volume);

    // Basic indicators
    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 12, values: closes });
    const emaSlowValues = EMA.calculate({ period: 26, values: closes });
    const ema200Values = EMA.calculate({ period: 200, values: closes });
    const smaValues = SMA.calculate({ period: 50, values: closes });
    
    // Advanced indicators
    const macdValues = MACD.calculate({
      values: closes,
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
      SimpleMAOscillator: false,
      SimpleMASignal: false
    });
    
    const bbValues = BollingerBands.calculate({
      values: closes,
      period: 20,
      stdDev: 2
    });
    
    const atrValues = ATR.calculate({
      high: highs,
      low: lows,
      close: closes,
      period: 14
    });
    
    const stochValues = Stochastic.calculate({
      high: highs,
      low: lows,
      close: closes,
      period: 14,
      signalPeriod: 3
    });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast: emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      ema200: ema200Values[ema200Values.length - 1] || closes[closes.length - 1],
      sma: smaValues[smaValues.length - 1] || closes[closes.length - 1],
      macd: macdValues[macdValues.length - 1] || { MACD: 0, signal: 0, histogram: 0 },
      bb: bbValues[bbValues.length - 1] || { upper: closes[closes.length - 1], middle: closes[closes.length - 1], lower: closes[closes.length - 1] },
      atr: atrValues[atrValues.length - 1] || 0,
      stoch: stochValues[stochValues.length - 1] || { k: 50, d: 50 },
      volume: volumes[volumes.length - 1],
      avgVolume: volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length,
      closes: closes,
      highs: highs,
      lows: lows
    };
  }

  predictMarketDirection(candle, indicators, index) {
    // Advanced market prediction algorithm
    let bullishSignals = 0;
    let bearishSignals = 0;
    let signalStrength = 0;
    
    const currentPrice = candle.close;
    const atrPercent = indicators.atr / currentPrice;
    
    // 1. Trend Analysis
    if (indicators.emaFast > indicators.emaSlow && indicators.emaSlow > indicators.ema200) {
      bullishSignals += 3; // Strong uptrend
    } else if (indicators.emaFast > indicators.emaSlow) {
      bullishSignals += 1; // Weak uptrend
    }
    
    if (indicators.emaFast < indicators.emaSlow && indicators.emaSlow < indicators.ema200) {
      bearishSignals += 3; // Strong downtrend
    } else if (indicators.emaFast < indicators.emaSlow) {
      bearishSignals += 1; // Weak downtrend
    }
    
    // 2. Momentum Analysis
    if (indicators.rsi > 70 && indicators.macd.MACD > indicators.macd.signal) {
      bullishSignals += 2; // Overbought but momentum strong
    } else if (indicators.rsi < 30 && indicators.macd.MACD < indicators.macd.signal) {
      bearishSignals += 2; // Oversold but momentum weak
    }
    
    // 3. Volatility Breakout Detection
    if (atrPercent > 0.03) { // High volatility
      if (currentPrice > indicators.bb.upper) {
        bullishSignals += 2; // Breakout above BB
      } else if (currentPrice < indicators.bb.lower) {
        bearishSignals += 2; // Breakdown below BB
      }
    }
    
    // 4. Volume Confirmation
    const volumeRatio = indicators.volume / indicators.avgVolume;
    if (volumeRatio > 2) {
      signalStrength += 2; // High volume confirmation
    } else if (volumeRatio > 1.5) {
      signalStrength += 1; // Medium volume confirmation
    }
    
    // 5. Stochastic Analysis
    if (indicators.stoch.k > 80 && indicators.stoch.d > 80) {
      bearishSignals += 1; // Overbought stochastic
    } else if (indicators.stoch.k < 20 && indicators.stoch.d < 20) {
      bullishSignals += 1; // Oversold stochastic
    }
    
    // 6. Price Action Analysis
    const recentCandles = this.data.slice(Math.max(0, index - 5), index + 1);
    const higherHighs = recentCandles.every((c, i) => i === 0 || c.high >= recentCandles[i-1].high);
    const lowerLows = recentCandles.every((c, i) => i === 0 || c.low <= recentCandles[i-1].low);
    
    if (higherHighs) bullishSignals += 2;
    if (lowerLows) bearishSignals += 2;
    
    // Calculate final prediction
    const netSignal = bullishSignals - bearishSignals;
    const totalSignals = bullishSignals + bearishSignals;
    const confidence = (Math.abs(netSignal) / Math.max(totalSignals, 1)) * (signalStrength / 4);
    
    return {
      direction: netSignal > 0 ? "BULLISH" : "BEARISH",
      strength: Math.abs(netSignal),
      confidence: Math.min(confidence, 1),
      bullishSignals,
      bearishSignals,
      signalStrength,
      atrPercent,
      volumeRatio
    };
  }

  shouldEnterPosition(candle, indicators, prevCandle, index) {
    const prediction = this.predictMarketDirection(candle, indicators, index);
    
    // Only enter high-confidence predictions for maximum profit
    if (prediction.confidence < 0.7) {
      return { enter: false, side: null, reason: "LOW_CONFIDENCE", confidence: prediction.confidence };
    }
    
    // Additional filters for maximum profit trades
    const volumeRatio = indicators.volume / indicators.avgVolume;
    const momentum = Math.abs((candle.close - prevCandle.close) / prevCandle.close);
    const atrPercent = indicators.atr / candle.close;
    
    // Look for explosive setups
    const explosiveSetup = (
      volumeRatio > 2.0 && // High volume
      momentum > 0.015 && // Strong momentum (1.5%+)
      atrPercent > 0.025 && // High volatility
      prediction.strength >= 4 // Strong signal
    );
    
    // Look for breakout setups
    const breakoutSetup = (
      (candle.close > indicators.bb.upper || candle.close < indicators.bb.lower) &&
      volumeRatio > 1.5 &&
      prediction.confidence > 0.8
    );
    
    // Look for trend continuation setups
    const trendSetup = (
      prediction.strength >= 5 &&
      prediction.confidence > 0.85 &&
      volumeRatio > 1.3
    );
    
    if (explosiveSetup) {
      return {
        enter: true,
        side: prediction.direction === "BULLISH" ? "LONG" : "SHORT",
        reason: "EXPLOSIVE_SETUP",
        confidence: prediction.confidence,
        prediction
      };
    }
    
    if (breakoutSetup) {
      return {
        enter: true,
        side: prediction.direction === "BULLISH" ? "LONG" : "SHORT",
        reason: "BREAKOUT_SETUP",
        confidence: prediction.confidence,
        prediction
      };
    }
    
    if (trendSetup) {
      return {
        enter: true,
        side: prediction.direction === "BULLISH" ? "LONG" : "SHORT",
        reason: "TREND_CONTINUATION",
        confidence: prediction.confidence,
        prediction
      };
    }
    
    return { enter: false, side: null, reason: "NO_SETUP", confidence: prediction.confidence };
  }

  shouldExit(candle, indicators) {
    if (!this.position.side) return { exit: false, reason: "NO_POSITION" };

    const currentPrice = candle.close;

    if (this.position.side === "LONG") {
      if (currentPrice <= this.position.stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice >= this.position.takeProfit) return { exit: true, reason: "TAKE_PROFIT" };

      // Dynamic exits for maximum profit
      if (indicators.rsi > 90) return { exit: true, reason: "EXTREME_OVERBOUGHT" };

      // Trailing stop for massive winners
      const profitPercent = (currentPrice - this.position.entryPrice) / this.position.entryPrice;
      if (profitPercent > this.currentTakeProfit * 0.5) {
        const trailingStop = this.position.entryPrice * (1 + profitPercent * 0.85);
        if (currentPrice < trailingStop) {
          return { exit: true, reason: "TRAILING_STOP" };
        }
      }

    } else { // SHORT
      if (currentPrice >= this.position.stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice <= this.position.takeProfit) return { exit: true, reason: "TAKE_PROFIT" };

      // Dynamic exits for maximum profit
      if (indicators.rsi < 10) return { exit: true, reason: "EXTREME_OVERSOLD" };

      // Trailing stop for massive winners
      const profitPercent = (this.position.entryPrice - currentPrice) / this.position.entryPrice;
      if (profitPercent > this.currentTakeProfit * 0.5) {
        const trailingStop = this.position.entryPrice * (1 - profitPercent * 0.85);
        if (currentPrice > trailingStop) {
          return { exit: true, reason: "TRAILING_STOP" };
        }
      }
    }

    return { exit: false, reason: "HOLD" };
  }

  openPosition(side, candle, reason, confidence, prediction) {
    const positionSize = this.balance * this.currentPositionSize;
    const leverage = this.currentLeverage;
    const contractSize = (positionSize * leverage) / candle.close;

    // Dynamic stop loss and take profit based on prediction strength
    let stopLossPercent = this.currentStopLoss;
    let takeProfitPercent = this.currentTakeProfit;

    // Adjust based on prediction confidence and strength
    if (confidence > 0.9 && prediction.strength >= 6) {
      takeProfitPercent *= 1.5; // Even higher targets for ultra-high confidence
      stopLossPercent *= 1.3; // Wider stops for big moves
    } else if (confidence > 0.8) {
      takeProfitPercent *= 1.2; // Higher targets for high confidence
      stopLossPercent *= 1.1; // Slightly wider stops
    }

    let stopLoss, takeProfit;

    if (side === "LONG") {
      stopLoss = candle.close * (1 - stopLossPercent);
      takeProfit = candle.close * (1 + takeProfitPercent);
    } else {
      stopLoss = candle.close * (1 + stopLossPercent);
      takeProfit = candle.close * (1 - takeProfitPercent);
    }

    this.position = {
      side,
      size: contractSize,
      entryPrice: candle.close,
      entryTime: candle.time,
      leverage,
      entryReason: reason,
      stopLoss,
      takeProfit
    };

    console.log(`🚀 OPEN ${side} at ${candle.close.toFixed(2)} | Size: ${positionSize.toFixed(2)} USDT (${leverage}x)`);
    console.log(`   Prediction: ${prediction.direction} | Confidence: ${(confidence * 100).toFixed(1)}% | Strength: ${prediction.strength}`);
    console.log(`   SL: ${stopLoss.toFixed(2)} (-${(stopLossPercent * 100).toFixed(1)}%) | TP: ${takeProfit.toFixed(2)} (+${(takeProfitPercent * 100).toFixed(1)}%)`);
    console.log(`   Signals: Bull=${prediction.bullishSignals}, Bear=${prediction.bearishSignals} | Vol: ${prediction.volumeRatio.toFixed(1)}x`);
    console.log(`   Reason: ${reason} | Balance: ${this.balance.toFixed(2)}`);
  }

  closePosition(candle, reason) {
    if (!this.position.side) return;

    const exitPrice = candle.close;
    const entryPrice = this.position.entryPrice;
    const size = this.position.size;

    let pnl = 0;
    if (this.position.side === "LONG") {
      pnl = size * (exitPrice - entryPrice);
    } else {
      pnl = size * (entryPrice - exitPrice);
    }

    const pnlPercent = (pnl / (this.balance * this.currentPositionSize)) * 100;
    const balanceBefore = this.balance;
    this.balance += pnl;

    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }
    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    const trade = {
      side: this.position.side,
      entryPrice,
      exitPrice,
      entryTime: this.position.entryTime,
      exitTime: candle.time,
      size,
      pnl,
      pnlPercent,
      reason,
      entryReason: this.position.entryReason,
      leverage: this.position.leverage,
      balanceBefore,
      balanceAfter: this.balance
    };

    this.trades.push(trade);

    const pnlColor = pnl > 0 ? "🟢" : "🔴";
    const totalValue = this.balance + this.totalWithdrawn;
    const progress = (totalValue / this.TARGET_TOTAL * 100).toFixed(2);

    console.log(`${pnlColor} CLOSE ${this.position.side} | PnL: ${pnl.toFixed(2)} USDT (${pnlPercent.toFixed(2)}%)`);
    console.log(`   Entry: ${entryPrice.toFixed(2)} → Exit: ${exitPrice.toFixed(2)} | ${reason}`);
    console.log(`   Balance: ${this.balance.toFixed(2)} | Total Value: ${totalValue.toFixed(2)} | Progress: ${progress}%`);

    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "", stopLoss: 0, takeProfit: 0 };
  }

  async runBacktest() {
    console.log(`\n🚀 Starting Market Prediction Strategy Backtest`);
    console.log(`📊 Data points: ${this.data.length}`);
    console.log(`⏰ Period: ${this.data[0]?.time} → ${this.data[this.data.length - 1]?.time}`);
    console.log(`🎯 Target: ${this.TARGET_TOTAL} USDT (${(this.TARGET_TOTAL / this.initialBalance).toFixed(0)}x return)`);

    for (let i = 200; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateAdvancedIndicators(i);

      // Exit logic
      if (this.position.side) {
        const exitSignal = this.shouldExit(candle, indicators);
        if (exitSignal.exit) {
          this.closePosition(candle, exitSignal.reason);
        }
      }

      // Entry logic (only if no position and sufficient balance)
      if (!this.position.side && this.balance >= 5) {
        const entrySignal = this.shouldEnterPosition(candle, indicators, prevCandle, i);

        if (entrySignal.enter) {
          this.openPosition(entrySignal.side, candle, entrySignal.reason, entrySignal.confidence, entrySignal.prediction);
        }
      }

      // Progress updates every 5000 candles
      if (i % 5000 === 0) {
        const totalValue = this.balance + this.totalWithdrawn;
        const progress = (totalValue / this.TARGET_TOTAL * 100).toFixed(2);
        console.log(`📊 Progress Update: ${totalValue.toFixed(2)} USDT (${progress}%) | Trades: ${this.trades.length}`);
      }

      // Early exit if target achieved
      const currentTotalValue = this.balance + this.totalWithdrawn;
      if (currentTotalValue >= this.TARGET_TOTAL) {
        console.log(`🎉 TARGET ACHIEVED! Stopping backtest early.`);
        break;
      }

      // Emergency stop if balance too low
      if (this.balance < 1) {
        console.log(`💥 BALANCE TOO LOW! Stopping backtest.`);
        break;
      }
    }

    // Close any remaining position
    if (this.position.side) {
      this.closePosition(this.data[this.data.length - 1], "END_OF_DATA");
    }

    this.printResults();
  }

  printResults() {
    const finalBalance = this.balance;
    const totalValue = finalBalance + this.totalWithdrawn;
    const totalReturn = totalValue - this.initialBalance;
    const returnPercent = (totalReturn / this.initialBalance) * 100;
    const returnMultiplier = totalValue / this.initialBalance;

    const winningTrades = this.trades.filter(t => t.pnl > 0);
    const losingTrades = this.trades.filter(t => t.pnl <= 0);
    const winRate = this.trades.length > 0 ? (winningTrades.length / this.trades.length) * 100 : 0;
    const avgWin = winningTrades.length > 0 ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length : 0;
    const profitFactor = Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : avgWin > 0 ? 10 : 0;
    const maxWin = winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.pnl)) : 0;
    const maxLoss = losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.pnl)) : 0;

    console.log("\n" + "=".repeat(80));
    console.log("🔮 MARKET PREDICTION STRATEGY - FINAL RESULTS");
    console.log("=".repeat(80));
    console.log(`💰 Initial Balance: ${this.initialBalance.toFixed(2)} USDT`);
    console.log(`💰 Final Balance: ${finalBalance.toFixed(2)} USDT`);
    console.log(`💎 Total Value: ${totalValue.toFixed(2)} USDT`);
    console.log(`📈 Total Return: ${totalReturn.toFixed(2)} USDT (${returnPercent.toFixed(2)}%)`);
    console.log(`🚀 Return Multiplier: ${returnMultiplier.toFixed(2)}x`);
    console.log(`🎯 Target Progress: ${(totalValue / this.TARGET_TOTAL * 100).toFixed(2)}% (${this.TARGET_TOTAL.toFixed(0)} USDT target)`);
    console.log(`🏆 Target Achieved: ${totalValue >= this.TARGET_TOTAL ? '✅ YES!' : '❌ NO'}`);
    console.log(`📊 Total Trades: ${this.trades.length}`);
    console.log(`🏆 Win Rate: ${winRate.toFixed(2)}%`);
    console.log(`📉 Max Drawdown: ${(this.maxDrawdown * 100).toFixed(2)}%`);
    console.log(`💎 Profit Factor: ${profitFactor.toFixed(2)}`);
    console.log(`🎯 Average Win: ${avgWin.toFixed(2)} USDT`);
    console.log(`💥 Average Loss: ${avgLoss.toFixed(2)} USDT`);
    console.log(`🚀 Largest Win: ${maxWin.toFixed(2)} USDT`);
    console.log(`💀 Largest Loss: ${maxLoss.toFixed(2)} USDT`);

    if (totalValue >= this.TARGET_TOTAL) {
      console.log("\n🎉 CONGRATULATIONS! 25,000 USDT TARGET ACHIEVED!");
      console.log(`🚀 Your ${this.initialBalance} USDT became ${totalValue.toFixed(2)} USDT!`);
      console.log(`💰 That's a ${returnMultiplier.toFixed(0)}x return!`);
    } else {
      console.log(`\n📈 Progress: ${(totalValue / this.TARGET_TOTAL * 100).toFixed(1)}% towards 25K target`);
      console.log(`💡 Strategy shows ${returnMultiplier.toFixed(2)}x return potential`);
      console.log(`🎯 Need ${(this.TARGET_TOTAL - totalValue).toFixed(2)} more USDT to reach target`);
    }

    // Save results
    const result = {
      strategy: "Market Prediction Strategy - Maximum Profit Focus",
      initialBalance: this.initialBalance,
      finalBalance,
      totalValue,
      totalReturn,
      returnPercent,
      returnMultiplier,
      maxBalance: this.maxBalance,
      maxDrawdown: this.maxDrawdown * 100,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,
      avgWin,
      avgLoss,
      maxWin,
      maxLoss,
      profitFactor,
      targetAchieved: totalValue >= this.TARGET_TOTAL,
      targetProgress: (totalValue / this.TARGET_TOTAL) * 100,
      trades: this.trades,
      dataStart: this.data[0]?.time,
      dataEnd: this.data[this.data.length - 1]?.time,
    };

    const resultsFile = `./results/backtests/market_prediction_${Date.now()}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to: ${resultsFile}`);
  }
}

// Run the strategy
async function main() {
  const strategy = new MarketPredictionStrategy(40);

  try {
    const dataFile = './data/history__15m_2025-06-21_2024-01-31T17.csv';
    console.log(`🚀 Running Market Prediction Strategy on: ${dataFile}`);

    await strategy.loadData(dataFile);
    await strategy.runBacktest();
  } catch (error) {
    console.error('❌ Error running backtest:', error);
  }
}

main().catch(console.error);
