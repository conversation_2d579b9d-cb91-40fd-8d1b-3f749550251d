const fs = require('fs');
const csv = require('csv-parser');
const { RSI, EMA, SMA, MACD } = require('technicalindicators');

class Balanced25kStrategy {
  constructor(initialBalance = 40) {
    this.data = [];
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "", stopLoss: 0, takeProfit: 0 };
    this.trades = [];
    this.cycleStartTime = "";
    this.totalWithdrawn = 0;
    this.maxBalance = initialBalance;
    this.maxDrawdown = 0;

    // BALANCED PARAMETERS FOR 625x TARGET
    this.TARGET_TOTAL = 25000;
    this.KEEP_BALANCE = 40;
    this.CYCLE_DURATION_DAYS = 30;
    
    // Progressive parameters that increase with success
    this.baseLeverage = 25;
    this.basePositionSize = 0.4; // Start with 40%
    this.baseStopLoss = 0.025; // 2.5%
    this.baseTakeProfit = 0.075; // 7.5% (3:1 R:R)
    
    // Current parameters (will adapt)
    this.currentLeverage = this.baseLeverage;
    this.currentPositionSize = this.basePositionSize;
    this.currentStopLoss = this.baseStopLoss;
    this.currentTakeProfit = this.baseTakeProfit;
    
    // RSI levels
    this.rsiOversold = 25;
    this.rsiOverbought = 75;
    this.minVolumeRatio = 1.3;

    // Performance tracking
    this.consecutiveLosses = 0;
    this.consecutiveWins = 0;
    this.totalTrades = 0;
    this.recentWinRate = 0;

    console.log("🎯 BALANCED 25K STRATEGY - PROGRESSIVE 625x TARGET");
    console.log(`💰 Initial Balance: ${initialBalance} USDT`);
    console.log(`🎯 Target Total: ${this.TARGET_TOTAL} USDT`);
    console.log(`📊 Starting Position Size: 40% (progressive)`);
    console.log(`⚡ Starting Leverage: 25x (progressive)`);
    console.log(`🎯 Take Profit: 7.5% | Stop Loss: 2.5%`);
    console.log(`⏰ Monthly Profit Withdrawal`);
  }

  async loadData(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on('end', () => {
          this.data = results;
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          if (results.length > 0) {
            this.cycleStartTime = results[0].time;
          }
          resolve();
        })
        .on('error', reject);
    });
  }

  shouldWithdrawProfits(currentTime) {
    const currentDate = new Date(currentTime);
    const cycleStartDate = new Date(this.cycleStartTime);
    const daysDiff = (currentDate.getTime() - cycleStartDate.getTime()) / (1000 * 60 * 60 * 24);
    return daysDiff >= this.CYCLE_DURATION_DAYS && this.balance > this.KEEP_BALANCE;
  }

  withdrawProfits(currentTime) {
    if (this.balance <= this.KEEP_BALANCE) return 0;
    
    const withdrawAmount = this.balance - this.KEEP_BALANCE;
    this.totalWithdrawn += withdrawAmount;
    this.balance = this.KEEP_BALANCE;
    
    console.log(`💸 PROFIT WITHDRAWAL at ${currentTime}`);
    console.log(`   Withdrawn: ${withdrawAmount.toFixed(2)} USDT`);
    console.log(`   Total Withdrawn: ${this.totalWithdrawn.toFixed(2)} USDT`);
    console.log(`   Total Value: ${(this.balance + this.totalWithdrawn).toFixed(2)} USDT`);
    console.log(`   Progress: ${((this.balance + this.totalWithdrawn) / this.TARGET_TOTAL * 100).toFixed(2)}%`);
    
    this.cycleStartTime = currentTime;
    this.adaptParameters();
    return withdrawAmount;
  }

  adaptParameters() {
    // Calculate recent performance
    const recentTrades = this.trades.slice(-20);
    const winningTrades = recentTrades.filter(t => t.pnl > 0);
    this.recentWinRate = recentTrades.length > 0 ? winningTrades.length / recentTrades.length : 0;
    
    const totalValue = this.balance + this.totalWithdrawn;
    const progressRatio = totalValue / this.TARGET_TOTAL;
    
    // Progressive scaling based on success and progress
    let leverageMultiplier = 1;
    let positionMultiplier = 1;
    let riskMultiplier = 1;
    
    // Increase aggression based on progress
    if (progressRatio > 0.5) {
      leverageMultiplier = 1.8;
      positionMultiplier = 1.6;
      riskMultiplier = 1.4;
    } else if (progressRatio > 0.2) {
      leverageMultiplier = 1.5;
      positionMultiplier = 1.3;
      riskMultiplier = 1.2;
    } else if (progressRatio > 0.05) {
      leverageMultiplier = 1.3;
      positionMultiplier = 1.2;
      riskMultiplier = 1.1;
    }
    
    // Adjust based on recent performance
    if (this.recentWinRate > 0.7) {
      leverageMultiplier *= 1.2;
      positionMultiplier *= 1.1;
    } else if (this.recentWinRate < 0.4) {
      leverageMultiplier *= 0.8;
      positionMultiplier *= 0.9;
      riskMultiplier *= 0.9;
    }
    
    // Apply multipliers
    this.currentLeverage = Math.min(60, Math.max(15, this.baseLeverage * leverageMultiplier));
    this.currentPositionSize = Math.min(0.8, Math.max(0.2, this.basePositionSize * positionMultiplier));
    this.currentStopLoss = this.baseStopLoss * riskMultiplier;
    this.currentTakeProfit = this.baseTakeProfit * riskMultiplier;
    
    console.log(`🧠 ADAPTING PARAMETERS (Progress: ${(progressRatio * 100).toFixed(2)}%, Win Rate: ${(this.recentWinRate * 100).toFixed(1)}%):`);
    console.log(`   → Leverage: ${this.currentLeverage.toFixed(0)}x`);
    console.log(`   → Position Size: ${(this.currentPositionSize * 100).toFixed(0)}%`);
    console.log(`   → Stop Loss: ${(this.currentStopLoss * 100).toFixed(1)}%`);
    console.log(`   → Take Profit: ${(this.currentTakeProfit * 100).toFixed(1)}%`);
  }

  calculateIndicators(index) {
    const lookback = Math.min(100, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);

    const closes = candles.map(c => c.close);
    const volumes = candles.map(c => c.volume);

    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 12, values: closes });
    const emaSlowValues = EMA.calculate({ period: 26, values: closes });
    const smaValues = SMA.calculate({ period: 50, values: closes });
    const macdValues = MACD.calculate({
      values: closes,
      fastPeriod: 12,
      slowPeriod: 26,
      signalPeriod: 9,
      SimpleMAOscillator: false,
      SimpleMASignal: false
    });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast: emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      sma: smaValues[smaValues.length - 1] || closes[closes.length - 1],
      macd: macdValues[macdValues.length - 1] || { MACD: 0, signal: 0, histogram: 0 },
      volume: volumes[volumes.length - 1],
      avgVolume: volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length
    };
  }

  shouldEnterLong(candle, indicators, prevCandle) {
    // Conservative but effective entry conditions
    const oversold = indicators.rsi < this.rsiOversold;
    const emaGoldenCross = indicators.emaFast > indicators.emaSlow * 1.002;
    const aboveSMA = candle.close > indicators.sma;
    const greenCandle = candle.close > candle.open;
    const volumeRatio = indicators.avgVolume > 0 ? indicators.volume / indicators.avgVolume : 1;
    const goodVolume = volumeRatio > this.minVolumeRatio;
    const momentum = Math.abs((candle.close - prevCandle.close) / prevCandle.close) > 0.008;
    const macdBullish = indicators.macd.MACD > indicators.macd.signal;
    
    // High confidence entries
    if (oversold && emaGoldenCross && aboveSMA && goodVolume && greenCandle) {
      return { enter: true, reason: "OVERSOLD_GOLDEN_CROSS_VOLUME", confidence: 0.9 };
    }
    
    if (emaGoldenCross && momentum && goodVolume && macdBullish && greenCandle) {
      return { enter: true, reason: "MOMENTUM_BREAKOUT_CONFIRMED", confidence: 0.8 };
    }
    
    if (oversold && aboveSMA && goodVolume && macdBullish) {
      return { enter: true, reason: "OVERSOLD_BOUNCE_CONFIRMED", confidence: 0.7 };
    }

    return { enter: false, reason: "NO_SIGNAL", confidence: 0 };
  }

  shouldEnterShort(candle, indicators, prevCandle) {
    // Conservative but effective short entry conditions
    const overbought = indicators.rsi > this.rsiOverbought;
    const emaDeathCross = indicators.emaFast < indicators.emaSlow * 0.998;
    const belowSMA = candle.close < indicators.sma;
    const redCandle = candle.close < candle.open;
    const volumeRatio = indicators.avgVolume > 0 ? indicators.volume / indicators.avgVolume : 1;
    const goodVolume = volumeRatio > this.minVolumeRatio;
    const momentum = Math.abs((candle.close - prevCandle.close) / prevCandle.close) > 0.008;
    const macdBearish = indicators.macd.MACD < indicators.macd.signal;
    
    // High confidence entries
    if (overbought && emaDeathCross && belowSMA && goodVolume && redCandle) {
      return { enter: true, reason: "OVERBOUGHT_DEATH_CROSS_VOLUME", confidence: 0.9 };
    }
    
    if (emaDeathCross && momentum && goodVolume && macdBearish && redCandle) {
      return { enter: true, reason: "MOMENTUM_BREAKDOWN_CONFIRMED", confidence: 0.8 };
    }
    
    if (overbought && belowSMA && goodVolume && macdBearish) {
      return { enter: true, reason: "OVERBOUGHT_REJECTION_CONFIRMED", confidence: 0.7 };
    }

    return { enter: false, reason: "NO_SIGNAL", confidence: 0 };
  }

  shouldExit(candle, indicators) {
    if (!this.position.side) return { exit: false, reason: "NO_POSITION" };

    const currentPrice = candle.close;
    
    if (this.position.side === "LONG") {
      if (currentPrice <= this.position.stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice >= this.position.takeProfit) return { exit: true, reason: "TAKE_PROFIT" };
      
      // Additional exit conditions
      if (indicators.rsi > 85) return { exit: true, reason: "EXTREME_OVERBOUGHT" };
      
      // Trailing stop for big winners
      const profitPercent = (currentPrice - this.position.entryPrice) / this.position.entryPrice;
      if (profitPercent > this.currentTakeProfit * 0.7) {
        const trailingStop = this.position.entryPrice * (1 + profitPercent * 0.8);
        if (currentPrice < trailingStop) {
          return { exit: true, reason: "TRAILING_STOP" };
        }
      }
      
    } else { // SHORT
      if (currentPrice >= this.position.stopLoss) return { exit: true, reason: "STOP_LOSS" };
      if (currentPrice <= this.position.takeProfit) return { exit: true, reason: "TAKE_PROFIT" };
      
      // Additional exit conditions
      if (indicators.rsi < 15) return { exit: true, reason: "EXTREME_OVERSOLD" };
      
      // Trailing stop for big winners
      const profitPercent = (this.position.entryPrice - currentPrice) / this.position.entryPrice;
      if (profitPercent > this.currentTakeProfit * 0.7) {
        const trailingStop = this.position.entryPrice * (1 - profitPercent * 0.8);
        if (currentPrice > trailingStop) {
          return { exit: true, reason: "TRAILING_STOP" };
        }
      }
    }

    return { exit: false, reason: "HOLD" };
  }

  openPosition(side, candle, reason, confidence) {
    const positionSize = this.balance * this.currentPositionSize;
    const leverage = this.currentLeverage;
    const contractSize = (positionSize * leverage) / candle.close;

    let stopLoss, takeProfit;
    
    if (side === "LONG") {
      stopLoss = candle.close * (1 - this.currentStopLoss);
      takeProfit = candle.close * (1 + this.currentTakeProfit);
    } else {
      stopLoss = candle.close * (1 + this.currentStopLoss);
      takeProfit = candle.close * (1 - this.currentTakeProfit);
    }

    this.position = {
      side,
      size: contractSize,
      entryPrice: candle.close,
      entryTime: candle.time,
      leverage,
      entryReason: reason,
      stopLoss,
      takeProfit
    };

    console.log(`🟢 OPEN ${side} at ${candle.close.toFixed(2)} | Size: ${positionSize.toFixed(2)} USDT (${leverage}x)`);
    console.log(`   SL: ${stopLoss.toFixed(2)} | TP: ${takeProfit.toFixed(2)} | Balance: ${this.balance.toFixed(2)}`);
    console.log(`   Reason: ${reason} | Confidence: ${(confidence * 100).toFixed(1)}%`);
  }

  closePosition(candle, reason) {
    if (!this.position.side) return;

    const exitPrice = candle.close;
    const entryPrice = this.position.entryPrice;
    const size = this.position.size;

    let pnl = 0;
    if (this.position.side === "LONG") {
      pnl = size * (exitPrice - entryPrice);
    } else {
      pnl = size * (entryPrice - exitPrice);
    }

    const pnlPercent = (pnl / (this.balance * this.currentPositionSize)) * 100;
    const balanceBefore = this.balance;
    this.balance += pnl;

    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }
    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    if (pnl > 0) {
      this.consecutiveWins++;
      this.consecutiveLosses = 0;
    } else {
      this.consecutiveLosses++;
      this.consecutiveWins = 0;
    }

    const trade = {
      side: this.position.side,
      entryPrice,
      exitPrice,
      entryTime: this.position.entryTime,
      exitTime: candle.time,
      size,
      pnl,
      pnlPercent,
      reason,
      entryReason: this.position.entryReason,
      leverage: this.position.leverage,
      balanceBefore,
      balanceAfter: this.balance
    };

    this.trades.push(trade);
    this.totalTrades++;

    const pnlColor = pnl > 0 ? "🟢" : "🔴";
    const totalValue = this.balance + this.totalWithdrawn;
    const progress = (totalValue / this.TARGET_TOTAL * 100).toFixed(2);

    console.log(`${pnlColor} CLOSE ${this.position.side} | PnL: ${pnl.toFixed(2)} (${pnlPercent.toFixed(2)}%)`);
    console.log(`   Balance: ${this.balance.toFixed(2)} | Total Value: ${totalValue.toFixed(2)} | Progress: ${progress}%`);
    console.log(`   ${reason} | Trade #${this.totalTrades}`);

    this.position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "", stopLoss: 0, takeProfit: 0 };
  }

  async runBacktest() {
    console.log(`\n🚀 Starting Balanced 25K Strategy Backtest`);
    console.log(`📊 Data points: ${this.data.length}`);
    console.log(`⏰ Period: ${this.data[0]?.time} → ${this.data[this.data.length - 1]?.time}`);
    console.log(`🎯 Target: ${this.TARGET_TOTAL} USDT (${(this.TARGET_TOTAL / this.initialBalance).toFixed(0)}x return)`);

    for (let i = 50; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      // Check for profit withdrawal
      if (this.shouldWithdrawProfits(candle.time)) {
        this.withdrawProfits(candle.time);
      }

      // Exit logic
      if (this.position.side) {
        const exitSignal = this.shouldExit(candle, indicators);
        if (exitSignal.exit) {
          this.closePosition(candle, exitSignal.reason);
        }
      }

      // Entry logic (only if no position and sufficient balance)
      if (!this.position.side && this.balance >= 15) {
        const longSignal = this.shouldEnterLong(candle, indicators, prevCandle);
        const shortSignal = this.shouldEnterShort(candle, indicators, prevCandle);

        // Enter trades with confidence threshold
        if (longSignal.enter && longSignal.confidence >= 0.7) {
          this.openPosition("LONG", candle, longSignal.reason, longSignal.confidence);
        } else if (shortSignal.enter && shortSignal.confidence >= 0.7) {
          this.openPosition("SHORT", candle, shortSignal.reason, shortSignal.confidence);
        }
      }

      // Risk management - reduce position size after losses
      if (this.consecutiveLosses >= 2) {
        this.currentPositionSize = Math.max(0.2, this.currentPositionSize * 0.9);
        if (this.consecutiveLosses >= 3 && this.position.side) {
          this.closePosition(candle, "EMERGENCY_STOP");
          console.log("⚠️ EMERGENCY STOP: 3 consecutive losses");
        }
      }

      // Increase position size after wins
      if (this.consecutiveWins >= 3) {
        this.currentPositionSize = Math.min(0.8, this.currentPositionSize * 1.05);
      }

      // Progress updates every 3000 candles
      if (i % 3000 === 0) {
        const totalValue = this.balance + this.totalWithdrawn;
        const progress = (totalValue / this.TARGET_TOTAL * 100).toFixed(2);
        console.log(`📊 Progress Update: ${totalValue.toFixed(2)} USDT (${progress}%) | Trades: ${this.trades.length}`);
      }

      // Early exit if target achieved
      const currentTotalValue = this.balance + this.totalWithdrawn;
      if (currentTotalValue >= this.TARGET_TOTAL) {
        console.log(`🎉 TARGET ACHIEVED! Stopping backtest early.`);
        break;
      }
    }

    // Close any remaining position
    if (this.position.side) {
      this.closePosition(this.data[this.data.length - 1], "END_OF_DATA");
    }

    this.printResults();
  }

  printResults() {
    const finalBalance = this.balance;
    const totalValue = finalBalance + this.totalWithdrawn;
    const totalReturn = totalValue - this.initialBalance;
    const returnPercent = (totalReturn / this.initialBalance) * 100;
    const returnMultiplier = totalValue / this.initialBalance;

    const winningTrades = this.trades.filter(t => t.pnl > 0);
    const losingTrades = this.trades.filter(t => t.pnl <= 0);
    const winRate = this.trades.length > 0 ? (winningTrades.length / this.trades.length) * 100 : 0;
    const avgWin = winningTrades.length > 0 ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) / winningTrades.length : 0;
    const avgLoss = losingTrades.length > 0 ? losingTrades.reduce((sum, t) => sum + t.pnl, 0) / losingTrades.length : 0;
    const profitFactor = Math.abs(avgLoss) > 0 ? avgWin / Math.abs(avgLoss) : avgWin > 0 ? 10 : 0;

    console.log("\n" + "=".repeat(80));
    console.log("🎯 BALANCED 25K STRATEGY - FINAL RESULTS");
    console.log("=".repeat(80));
    console.log(`💰 Initial Balance: ${this.initialBalance.toFixed(2)} USDT`);
    console.log(`💰 Final Balance: ${finalBalance.toFixed(2)} USDT`);
    console.log(`💸 Total Withdrawn: ${this.totalWithdrawn.toFixed(2)} USDT`);
    console.log(`💎 Total Value: ${totalValue.toFixed(2)} USDT`);
    console.log(`📈 Total Return: ${totalReturn.toFixed(2)} USDT (${returnPercent.toFixed(2)}%)`);
    console.log(`🚀 Return Multiplier: ${returnMultiplier.toFixed(2)}x`);
    console.log(`🎯 Target Progress: ${(totalValue / this.TARGET_TOTAL * 100).toFixed(2)}% (${this.TARGET_TOTAL.toFixed(0)} USDT target)`);
    console.log(`🏆 Target Achieved: ${totalValue >= this.TARGET_TOTAL ? '✅ YES!' : '❌ NO'}`);
    console.log(`📊 Total Trades: ${this.trades.length}`);
    console.log(`🏆 Win Rate: ${winRate.toFixed(2)}%`);
    console.log(`📉 Max Drawdown: ${(this.maxDrawdown * 100).toFixed(2)}%`);
    console.log(`💎 Profit Factor: ${profitFactor.toFixed(2)}`);

    if (totalValue >= this.TARGET_TOTAL) {
      console.log("\n🎉 CONGRATULATIONS! 25,000 USDT TARGET ACHIEVED!");
      console.log(`🚀 Your ${this.initialBalance} USDT became ${totalValue.toFixed(2)} USDT!`);
      console.log(`💰 That's a ${returnMultiplier.toFixed(0)}x return!`);
    } else {
      console.log(`\n📈 Progress: ${(totalValue / this.TARGET_TOTAL * 100).toFixed(1)}% towards 25K target`);
      console.log(`💡 Strategy shows ${returnMultiplier.toFixed(2)}x return potential`);
      console.log(`🎯 Need ${(this.TARGET_TOTAL - totalValue).toFixed(2)} more USDT to reach target`);
    }

    // Save results
    const result = {
      strategy: "Balanced 25K Strategy",
      initialBalance: this.initialBalance,
      finalBalance,
      totalWithdrawn: this.totalWithdrawn,
      totalValue,
      totalReturn,
      returnPercent,
      returnMultiplier,
      maxBalance: this.maxBalance,
      maxDrawdown: this.maxDrawdown * 100,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate,
      avgWin,
      avgLoss,
      profitFactor,
      targetAchieved: totalValue >= this.TARGET_TOTAL,
      targetProgress: (totalValue / this.TARGET_TOTAL) * 100,
      trades: this.trades,
      dataStart: this.data[0]?.time,
      dataEnd: this.data[this.data.length - 1]?.time,
    };

    const resultsFile = `./results/backtests/balanced_25k_${Date.now()}.json`;
    fs.writeFileSync(resultsFile, JSON.stringify(result, null, 2));
    console.log(`\n💾 Results saved to: ${resultsFile}`);
  }
}

// Run the strategy
async function main() {
  const strategy = new Balanced25kStrategy(40);

  try {
    const dataFile = './data/history__15m_2025-06-21T13:54:59.060Z_2024-01-31T17:00:00.000Z.csv';
    console.log(`🚀 Running Balanced 25K Strategy on: ${dataFile}`);

    await strategy.loadData(dataFile);
    await strategy.runBacktest();
  } catch (error) {
    console.error('❌ Error running backtest:', error);
  }
}

main().catch(console.error);
